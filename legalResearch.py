"""
LegalResearch: Interactive Legal Research Assistant

An agentic AI framework for interactive legal research and question answering,
specializing in Indian law with focus on various legal codes and precedents.

This framework provides an interactive legal research experience with clarifying
questions before providing comprehensive answers to legal queries.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Union
import re
import json
import uuid
from datetime import datetime
from dotenv import load_dotenv

# LangChain imports
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.runnables import Runnable, RunnableConfig
from langchain_core.output_parsers import StrOutputParser, JsonOutputParser

# LLM providers
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI

# Search tools
from langchain_tavily import TavilySearch
from langchain_google_community import GoogleSearchAPIWrapper

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("LegalResearch")

# Get API keys from environment variables and strip quotes if present
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "").strip('"')
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "").strip('"')
TAVILY_API_KEY = "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"
GOOGLE_API_KEY = "AIzaSyCRQwmfxIpWV8FvMst9ePTaRO"
GOOGLE_CSE_ID = "465258ebeb5384150"

# Debug: Print API key status (first few characters only for security)
print(f"🔑 API Keys Status:")
print(f"   TAVILY_API_KEY: {'✅ Found' if TAVILY_API_KEY else '❌ Missing'} ({TAVILY_API_KEY[:8]}... if found)")
print(f"   GOOGLE_API_KEY: {'✅ Found' if GOOGLE_API_KEY else '❌ Missing'} ({GOOGLE_API_KEY[:8]}... if found)")
print(f"   GOOGLE_CSE_ID: {'✅ Found' if GOOGLE_CSE_ID else '❌ Missing'} ({GOOGLE_CSE_ID[:8]}... if found)")

# Set environment variables explicitly for any libraries that still look for them
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY or ""
os.environ["GOOGLE_CSE_ID"] = GOOGLE_CSE_ID or ""
os.environ["TAVILY_API_KEY"] = TAVILY_API_KEY or ""

# Indian Legal Websites for targeted search
INDIAN_LEGAL_WEBSITES = [
    "indiankanoon.org",
    "sci.gov.in",
    "delhihighcourt.nic.in",
    "bombayhighcourt.nic.in",
    "mhc.tn.gov.in",
    "hcraj.nic.in",
    "lawcommissionofindia.nic.in",
    "meity.gov.in",
    "cic.gov.in",
    "nhrc.nic.in",
    "advocatekhoj.com",
    "lawyersclubindia.com",
    "manupatra.com",
    "scconline.com"
]


class LegalResearchQuery:
    """Class for legal research queries submitted to the system."""

    def __init__(self, query_text, jurisdiction="india", language="english",
                 query_context=None, follow_up_info=None, conversation_id=None):
        """
        Initialize a legal research query.

        Args:
            query_text: The legal query text
            jurisdiction: Legal jurisdiction (india, international, etc.)
            language: Language of the query (english, hindi, etc.)
            query_context: Additional context for the query
            follow_up_info: Additional information provided in follow-up
            conversation_id: ID to track conversation state
        """
        self.query_text = query_text
        self.jurisdiction = jurisdiction
        self.language = language
        self.query_context = query_context or {}
        self.follow_up_info = follow_up_info or {}
        self.conversation_id = conversation_id or str(uuid.uuid4())


class LegalResearchResponse:
    """Class for structured legal research responses."""

    def __init__(self,
                is_complete=False,
                current_question=None,
                question_number=0,
                total_questions=3,
                answer=None,
                references=None,
                follow_up_suggestions=None,
                conversation_id=None):
        """
        Initialize a legal research response.

        Args:
            is_complete: Whether this response is final or needs more information
            current_question: The current question being asked (one at a time)
            question_number: The current question number (1-based)
            total_questions: Total number of questions to ask
            answer: The complete legal answer (if is_complete is True)
            references: Legal references and citations supporting the answer
            follow_up_suggestions: Suggested follow-up questions
            conversation_id: ID to track conversation state
        """
        self.is_complete = is_complete
        self.current_question = current_question
        self.question_number = question_number
        self.total_questions = total_questions
        self.answer = answer
        self.references = references or []
        self.follow_up_suggestions = follow_up_suggestions or []
        self.conversation_id = conversation_id


class LLMProvider:
    """Provider for LLM models."""

    @staticmethod
    def get_llm(provider: str = "gemini", model: Optional[str] = None, temperature: float = 0) -> Runnable:
        """
        Get a language model instance.

        Args:
            provider: The LLM provider to use (gemini, openai)
            model: Specific model to use (optional)
            temperature: Generation temperature (0-1)

        Returns:
            LLM instance
        """
        if provider == "gemini":
            if not GEMINI_API_KEY:
                raise ValueError("Gemini API key not found")

            # Select the right Gemini model
            if not model:
                model = "gemini-2.5-pro"

            return ChatGoogleGenerativeAI(
                model=model,
                google_api_key=GEMINI_API_KEY,
                temperature=temperature,
                max_output_tokens=8192,
                top_p=0.95,
                top_k=40,
                convert_system_message_to_human=True
            )
        elif provider == "openai":
            if not OPENAI_API_KEY:
                raise ValueError("OpenAI API key not found")

            # Select the right OpenAI model
            if not model:
                model = "gpt-4o"

            return ChatOpenAI(
                model=model,
                openai_api_key=OPENAI_API_KEY,
                temperature=temperature,
                max_tokens=4096
            )
        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")


class SearchEngineManager:
    """Manager for multiple search engines to get comprehensive results."""
    
    def __init__(self):
        """Initialize all available search engines."""
        self.search_engines = {}
        self.initialize_search_engines()
    
    def initialize_search_engines(self):
        """Initialize available search engines with improved configurations."""
        print("🔧 INITIALIZING ENHANCED SEARCH ENGINES...")
        print("="*50)

        # Initialize Tavily Search (Advanced AI-powered search)
        print("🟡 Initializing Tavily Search Engine...")
        try:
            if TAVILY_API_KEY:
                self.search_engines['tavily'] = TavilySearch(
                    max_results=15,
                    search_depth="advanced",
                    include_answer=True,
                    include_raw_content=True,
                    include_images=True,
                    include_links=True,
                    include_videos=True,
                    include_image_descriptions=True,
                    
                    include_domains=INDIAN_LEGAL_WEBSITES[:13]  # Target Indian legal sites
                )
                print("   ✅ Tavily search engine initialized with Indian legal sites focus")
                logger.info("✅ Tavily search engine initialized successfully")
            else:
                print("   ❌ Tavily API key not found in environment variables")
                logger.warning("❌ Tavily API key not found")
        except Exception as e:
            print(f"   ❌ Tavily initialization failed: {e}")
            logger.error(f"❌ Failed to initialize Tavily: {e}")

        # Initialize Google Search (Enhanced configuration for more results)
        print("\n🌐 Initializing Google Search Engine...")
        try:
            if GOOGLE_API_KEY and GOOGLE_CSE_ID:
                self.search_engines['google'] = GoogleSearchAPIWrapper(
                    google_api_key=GOOGLE_API_KEY,
                    google_cse_id=GOOGLE_CSE_ID,
                    search_engine="google",
                    k=10  # Increased to get at least 10 results
                )
                print("   ✅ Google search engine initialized successfully with 10 results")
                logger.info("✅ Google search engine initialized successfully")
            else:
                missing = []
                if not GOOGLE_API_KEY:
                    missing.append("GOOGLE_API_KEY")
                if not GOOGLE_CSE_ID:
                    missing.append("GOOGLE_CSE_ID")
                print(f"   ❌ Google search missing: {', '.join(missing)}")
                logger.warning(f"❌ Google API key or CSE ID not found: {missing}")
        except Exception as e:
            print(f"   ❌ Google initialization failed: {e}")
            logger.error(f"❌ Failed to initialize Google search: {e}")

        # Summary
        total_engines = len(self.search_engines)
        print(f"\n📊 ENHANCED SEARCH ENGINE INITIALIZATION COMPLETE:")
        print(f"   🎯 Successfully initialized: {total_engines}/2 search engines")
        print(f"   🔍 Active engines: {', '.join(self.search_engines.keys())}")
        print(f"   🇮🇳 Indian legal sites targeted: {len(INDIAN_LEGAL_WEBSITES)} sites")

        if total_engines == 0:
            print("   ⚠️ WARNING: No search engines available! Results will be limited.")
        elif total_engines < 2:
            print(f"   ⚠️ WARNING: Only {total_engines}/2 engines active. Add missing API keys for better results.")
        else:
            print("   ✅ SEARCH ENGINES READY FOR ENHANCED COLLABORATIVE SEARCH!")

        print("="*50)

    def enhanced_google_search(self, query: str) -> List[Dict[str, Any]]:
        """
        Enhanced Google search with comprehensive data extraction.

        Args:
            query: Search query

        Returns:
            List of search results with extracted data
        """
        results = []

        try:
            # Perform Google search
            google_results = self.search_engines['google'].run(query)

            if isinstance(google_results, str) and google_results.strip():
                # Parse Google results string format
                results_parts = google_results.split('\n\n')
                valid_results = [part for part in results_parts if part.strip()]

                for i, part in enumerate(valid_results, 1):
                    if part.strip():
                        # Extract title and content
                        lines = part.strip().split('\n')
                        title = lines[0] if lines else f'Google Result {i}'
                        content = '\n'.join(lines[1:]) if len(lines) > 1 else ''

                        # Extract URL from content with better regex
                        url_matches = re.findall(r'https?://[^\s\)\]\}]+', content)
                        url = url_matches[0] if url_matches else ''

                        # Clean URL if found
                        if url:
                            url = re.sub(r'[.,;:!?)\]}]+$', '', url)

                        # Extract more detailed information
                        snippet = content[:300] + '...' if len(content) > 300 else content

                        results.append({
                            'title': title,
                            'snippet': snippet,
                            'content': content,
                            'url': url,
                            'source': 'Google',
                            'rank': i
                        })

        except Exception as e:
            logger.error(f"Google enhanced search error: {e}")
            # Fallback result
            results.append({
                'title': f'Legal Query: {query}',
                'snippet': f'Search performed for: {query} (Google)',
                'content': f'Google search was attempted for the query: {query}',
                'url': f'https://www.google.com/search?q={query.replace(" ", "+")}',
                'source': 'Google',
                'rank': 1
            })

        return results[:10]  # Return up to 10 results
    
    def comprehensive_search(self, query: str) -> Dict[str, Any]:
        """
        Perform comprehensive search using available search engines.

        Args:
            query: Search query

        Returns:
            Comprehensive search results with detailed source tracking
        """
        all_results = {
            'combined_results': [],
            'links': [],
            'sources': {},
            'summary': '',
            'search_engines_used': [],
            'detailed_sources': {}
        }

        logger.info(f"🔍 Starting COMPREHENSIVE search for: '{query}'")
        print(f"\n🔍 SEARCH STARTING: '{query}'")
        print("="*70)

        # Search with Tavily First
        tavily_count = 0
        print("🟡 TAVILY SEARCH ENGINE:")
        if 'tavily' in self.search_engines:
            try:
                print("   🔍 Searching Tavily for legal information...")
                tavily_results = self.search_engines['tavily'].run(query)

                if isinstance(tavily_results, list) and tavily_results:
                    for i, result in enumerate(tavily_results, 1):
                        if isinstance(result, dict):
                            # Extract meaningful data
                            content = result.get('content', '')
                            url = result.get('url', '')
                            title = result.get('title', '')
                            score = result.get('score', 0)

                            if url and url not in all_results['links']:
                                all_results['links'].append(url)

                            all_results['combined_results'].append({
                                'source': 'Tavily',
                                'title': title,
                                'content': content[:500] + '...' if len(content) > 500 else content,
                                'url': url,
                                'relevance': 'high',
                                'search_rank': i,
                                'score': score
                            })

                            print(f"   ✅ Tavily Result {i}: {title[:50]}..." if title else f"   ✅ Tavily Result {i}: Found content")
                            if url:
                                print(f"      🔗 Link: {url}")

                    tavily_count = len(tavily_results)
                    all_results['sources']['tavily'] = tavily_count
                    all_results['search_engines_used'].append('Tavily')
                    all_results['detailed_sources']['tavily'] = {
                        'count': tavily_count,
                        'status': 'success',
                        'links_found': [r['url'] for r in all_results['combined_results'] if r['source'] == 'Tavily' and r['url']]
                    }

                    print(f"   📊 Tavily Summary: {tavily_count} results found")
                else:
                    print("   ⚠️ Tavily: No results returned")
                    all_results['detailed_sources']['tavily'] = {'count': 0, 'status': 'no_results'}

            except Exception as e:
                print(f"   ❌ Tavily Error: {e}")
                all_results['detailed_sources']['tavily'] = {'count': 0, 'status': 'error', 'error': str(e)}
                logger.error(f"❌ Tavily search failed: {e}")
        else:
            print("   ❌ Tavily: Not available (check API key)")
            all_results['detailed_sources']['tavily'] = {'count': 0, 'status': 'not_available'}

        # Search with Google (Enhanced with more comprehensive data extraction)
        google_count = 0
        print("\n🌐 GOOGLE SEARCH ENGINE:")
        if 'google' in self.search_engines:
            try:
                print("   🔍 Searching Google for comprehensive legal information...")

                # Use enhanced Google search
                google_results = self.enhanced_google_search(query)

                if google_results:
                    for result in google_results:
                        title = result.get('title', '')
                        content = result.get('content', '')
                        snippet = result.get('snippet', '')
                        url = result.get('url', '')
                        rank = result.get('rank', 0)

                        if url and url not in all_results['links']:
                            all_results['links'].append(url)

                        all_results['combined_results'].append({
                            'source': 'Google',
                            'title': title,
                            'content': content,
                            'snippet': snippet,
                            'url': url,
                            'relevance': 'high',
                            'search_rank': rank
                        })

                        print(f"   ✅ Google Result {rank}: {title[:50]}..." if title else f"   ✅ Google Result {rank}: Found content")
                        if url:
                            print(f"      🔗 Link: {url}")
                        if snippet:
                            print(f"      📄 Snippet: {snippet[:100]}...")

                    google_count = len(google_results)
                    all_results['sources']['google'] = google_count
                    all_results['search_engines_used'].append('Google')
                    all_results['detailed_sources']['google'] = {
                        'count': google_count,
                        'status': 'success',
                        'links_found': [r['url'] for r in all_results['combined_results'] if r['source'] == 'Google' and r['url']]
                    }

                    print(f"   📊 Google Summary: {google_count} results found with comprehensive data")
                else:
                    print("   ⚠️ Google: No results returned")
                    all_results['detailed_sources']['google'] = {'count': 0, 'status': 'no_results'}

            except Exception as e:
                print(f"   ❌ Google Error: {e}")
                all_results['detailed_sources']['google'] = {'count': 0, 'status': 'error', 'error': str(e)}
                logger.error(f"❌ Google search failed: {e}")
        else:
            print("   ❌ Google: Not available (check API key and CSE ID)")
            all_results['detailed_sources']['google'] = {'count': 0, 'status': 'not_available'}

        # Create comprehensive summary
        total_results = len(all_results['combined_results'])
        total_links = len(all_results['links'])
        engines_used = ', '.join(all_results['search_engines_used']) if all_results['search_engines_used'] else 'None'

        print(f"\n📊 SEARCH SUMMARY:")
        print(f"   🎯 Total Results: {total_results}")
        print(f"   🔗 Unique Links: {total_links}")
        print(f"   🔍 Engines Used: {engines_used}")
        print(f"   📈 Breakdown: Tavily({tavily_count}) + Google({google_count})")
        print("="*70)

        all_results['summary'] = f"Found {total_results} results from {engines_used} with {total_links} unique links"

        logger.info(f"🎯 SEARCH COMPLETE: {all_results['summary']}")
        return all_results


class GoogleSearchTool:
    """Enhanced Google search tool with comprehensive data extraction."""

    def __init__(self):
        """Initialize the Google search tool."""
        if not GOOGLE_API_KEY or not GOOGLE_CSE_ID:
            raise ValueError("Google API key or CSE ID not found")

        self.google_search = GoogleSearchAPIWrapper(
            google_api_key=GOOGLE_API_KEY,
            google_cse_id=GOOGLE_CSE_ID,
            k=10  # Get 10 results for comprehensive coverage
        )

    def search(self, query: str) -> str:
        """
        Search Google and format results with comprehensive data extraction.

        Args:
            query: Search query

        Returns:
            Enhanced formatted search results with comprehensive data
        """
        try:
            search_results = self.google_search.run(query)
            if not search_results:
                return "No results found."

            # Enhanced formatting with comprehensive data extraction
            formatted_results = []
            results_parts = search_results.split('\n\n')

            for i, result in enumerate(results_parts, 1):
                if result.strip():
                    lines = result.strip().split('\n')
                    title = lines[0] if lines else f"Result {i}"
                    content = '\n'.join(lines[1:]) if len(lines) > 1 else "No content available"

                    # Extract URLs more effectively
                    urls = re.findall(r'https?://[^\s\)\]\}]+', content)
                    clean_urls = []
                    for url in urls:
                        clean_url = re.sub(r'[.,;:!?)\]}]+$', '', url)
                        clean_urls.append(clean_url)

                    # Extract key information from content
                    snippet = content[:200] + '...' if len(content) > 200 else content

                    url_info = f"\n🔗 Links: {', '.join(clean_urls[:3])}" if clean_urls else ""
                    snippet_info = f"\n📄 Summary: {snippet}" if snippet != content else ""

                    formatted_results.append(f"📄 **{title}**\n{content}{snippet_info}{url_info}")

            return "\n\n" + "="*50 + "\n\n".join(formatted_results)

        except Exception as e:
            logger.error(f"Error in Google search: {e}")
            return f"Error performing search: {str(e)}"


class LegalResearchAgent:
    """Enhanced agent for interactive legal research with comprehensive search capabilities."""

    def __init__(self):
        """Initialize the enhanced legal research agent."""
        # Initialize language model
        try:
            self.llm = LLMProvider.get_llm(provider="gemini", temperature=0.3)  # Slightly higher temp for more human-like responses
            logger.info("✅ Successfully initialized Gemini LLM")
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize Gemini LLM: {e}. Falling back to OpenAI.")
            try:
                self.llm = LLMProvider.get_llm(provider="openai", temperature=0.3)
                logger.info("✅ Successfully initialized OpenAI LLM")
            except Exception as e2:
                logger.error(f"❌ Failed to initialize OpenAI LLM: {e2}")
                raise ValueError("Could not initialize any LLM provider")

        # Initialize comprehensive search manager
        self.search_manager = SearchEngineManager()
        self.has_search = len(self.search_manager.search_engines) > 0
        
        if self.has_search:
            logger.info(f"✅ Search engines ready: {list(self.search_manager.search_engines.keys())}")
        else:
            logger.warning("⚠️ No search engines available")

        # Initialize conversation store
        self.conversations = {}

    def _check_if_needs_clarification(self, query: LegalResearchQuery) -> Dict[str, Any]:
        """
        Check if the query needs clarification.

        Args:
            query: The legal research query

        Returns:
            Dictionary with clarification assessment
        """
        print("Checking if query needs clarification...")

        # Check if this is a continued conversation
        conversation_data = self.conversations.get(query.conversation_id, None)

        # If this is a follow-up in an existing conversation, we may already have the context
        if conversation_data and conversation_data.get("follow_up_provided", False):
            print("Follow-up information already provided, no clarification needed")
            return {"needs_clarification": False, "reason": "Follow-up information already provided"}

        # Generate dynamic clarification questions based on the query
        print("Generating dynamic clarification questions based on query")

        try:
            # Create a prompt for the LLM to generate clarification questions
            clarification_prompt = ChatPromptTemplate.from_messages([
                SystemMessage(content="""You are a legal research assistant analyzing legal queries.
                Your task is to identify 3 important clarifying questions that would help provide a more accurate and comprehensive answer.

                Focus on questions that:
                1. Address ambiguities or missing information in the query
                2. Seek relevant legal context (e.g., jurisdiction, time period, specific circumstances)
                3. Help identify applicable laws, precedents, or legal principles

                Return ONLY the 3 questions in a JSON array format like this:
                ["Question 1?", "Question 2?", "Question 3?"]

                Do not include any explanations, introductions, or other text."""),
                HumanMessage(content=f"Legal Query: {query.query_text}")
            ])

            # Get clarification questions from the LLM
            chain = clarification_prompt | self.llm | JsonOutputParser()
            clarifying_questions = chain.invoke({})

            # Ensure we have a list of questions (fallback if parsing fails)
            if not isinstance(clarifying_questions, list) or len(clarifying_questions) == 0:
                print("Failed to generate valid clarification questions, using fallback questions")
                clarifying_questions = [
                    "Could you provide more specific details about your legal situation?",
                    "Are there any particular circumstances or timeline considerations I should know about?",
                    "What outcome are you hoping to achieve or understand better?"
                ]

            # Limit to 3 questions
            clarifying_questions = clarifying_questions[:3]

            return {
                "needs_clarification": True,
                "reason": "Analysis indicates clarification would improve answer quality",
                "clarifying_questions": clarifying_questions
            }

        except Exception as e:
            print(f"Error generating clarification questions: {e}")
            # Fallback to default questions if there's an error
            return {
                "needs_clarification": True,
                "reason": "Using default questions due to error in analysis",
                "clarifying_questions": [
                    "Could you provide more specific details about your legal situation?",
                    "Are there any particular circumstances or timeline considerations I should know about?",
                    "What outcome are you hoping to achieve or understand better?"
                ]
            }

    def process_query(self, query: Union[str, LegalResearchQuery], follow_up_info: Optional[Dict[str, Any]] = None) -> LegalResearchResponse:
        """
        Process a legal research query with enhanced search capabilities.

        Args:
            query: The legal research query string or object
            follow_up_info: Answer to the current clarifying question

        Returns:
            LegalResearchResponse object
        """
        # Convert string query to LegalResearchQuery object if necessary
        if isinstance(query, str):
            query = LegalResearchQuery(query_text=query)

        # Get or initialize conversation state
        conversation = self.conversations.get(query.conversation_id, {
            "query": query.query_text,
            "question_number": 0,
            "answers": {},
            "clarifying_questions": []
        })

        # If this is a new conversation, generate all clarifying questions
        if not conversation.get("clarifying_questions"):
            clarification_assessment = self._check_if_needs_clarification(query)
            conversation["clarifying_questions"] = clarification_assessment.get("clarifying_questions", [
                "Could you provide more specific details about your legal situation?",
                "Are there any particular circumstances or timeline considerations I should know about?",
                "What outcome are you hoping to achieve or understand better?"
            ])
            conversation["total_questions"] = len(conversation["clarifying_questions"])
            self.conversations[query.conversation_id] = conversation

        # If follow_up_info is provided, store the answer and increment question counter
        if follow_up_info:
            current_question = conversation["question_number"]
            # Convert follow_up_info to string if it's not already a string
            if isinstance(follow_up_info, dict) and str(current_question) in follow_up_info:
                conversation["answers"][current_question] = follow_up_info[str(current_question)]
            else:
                conversation["answers"][current_question] = follow_up_info
            conversation["question_number"] += 1
            self.conversations[query.conversation_id] = conversation

        # If we haven't asked all questions yet, return the next question
        if conversation["question_number"] < len(conversation["clarifying_questions"]):
            current_q_num = conversation["question_number"]
            return LegalResearchResponse(
                is_complete=False,
                current_question=conversation["clarifying_questions"][current_q_num],
                question_number=current_q_num + 1,
                total_questions=len(conversation["clarifying_questions"]),
                conversation_id=query.conversation_id
            )

        # All questions have been answered, process final response
        try:
            # Update query with all collected answers
            query.follow_up_info = conversation["answers"]

            # Construct comprehensive prompt with all information
            full_prompt = self._construct_full_prompt(query)
            logger.info(f"📝 Generated comprehensive prompt with all collected information")

            # Perform comprehensive search using all available engines
            search_data = {"results": "No search performed", "links": [], "summary": "Search not available"}
            
            if self.has_search:
                logger.info("🔍 Starting comprehensive multi-engine search...")
                
                # Create targeted search queries
                search_queries = self._generate_search_queries(query)
                all_search_data = {"combined_results": [], "links": [], "sources": {}, "summaries": []}
                
                for search_query in search_queries:
                    logger.info(f"🔍 Searching: {search_query}")
                    search_result = self.search_manager.comprehensive_search(search_query)
                    
                    # Combine results
                    all_search_data["combined_results"].extend(search_result.get("combined_results", []))
                    all_search_data["links"].extend(search_result.get("links", []))
                    all_search_data["summaries"].append(search_result.get("summary", ""))
                    
                    # Merge source counts
                    for engine, count in search_result.get("sources", {}).items():
                        all_search_data["sources"][engine] = all_search_data["sources"].get(engine, 0) + count
                
                # Remove duplicate links
                all_search_data["links"] = list(dict.fromkeys(all_search_data["links"]))
                
                search_data = all_search_data
                logger.info(f"🎯 Search completed: {len(search_data['combined_results'])} total results, {len(search_data['links'])} unique links")

            # Generate enhanced response with human-like approach
            response_content = self._generate_human_like_response(query, full_prompt, search_data)

            # Extract enhanced references
            references = self._extract_enhanced_references(response_content, search_data)

            # Generate contextual follow-up suggestions
            follow_up_suggestions = self._generate_contextual_follow_ups(query, response_content)

            # Update conversation state
            conversation["answered"] = True
            conversation["answer"] = response_content
            conversation["references"] = references
            conversation["search_data"] = search_data
            self.conversations[query.conversation_id] = conversation

            # Return complete response
            return LegalResearchResponse(
                is_complete=True,
                answer=response_content,
                references=references,
                follow_up_suggestions=follow_up_suggestions,
                conversation_id=query.conversation_id
            )

        except Exception as e:
            logger.error(f"❌ Error processing query: {e}")
            return LegalResearchResponse(
                is_complete=True,
                answer=f"I apologize, but I encountered an issue while researching your question. Let me try to help with what I know: {str(e)}",
                conversation_id=query.conversation_id
            )

    def _construct_full_prompt(self, query: LegalResearchQuery) -> str:
        """
        Construct a comprehensive prompt with all available information.

        Args:
            query: The legal research query

        Returns:
            Comprehensive prompt string
        """
        # Start with the original query
        prompt_parts = [f"LEGAL QUESTION: {query.query_text}"]

        # Add jurisdiction if known
        if query.jurisdiction and query.jurisdiction != "india":
            prompt_parts.append(f"JURISDICTION: {query.jurisdiction}")

        # Add collected answers in sequence
        if query.follow_up_info:
            prompt_parts.append("ADDITIONAL INFORMATION PROVIDED:")
            for q_num_str, answer in sorted(query.follow_up_info.items()):
                # Convert string key to integer if needed
                q_num = int(q_num_str) if isinstance(q_num_str, str) else q_num_str
                if q_num < len(self.conversations[query.conversation_id]["clarifying_questions"]):
                    question = self.conversations[query.conversation_id]["clarifying_questions"][q_num]
                    prompt_parts.append(f"Q{q_num + 1}: {question}")
                    prompt_parts.append(f"A{q_num + 1}: {answer}")

        # Add query context if available
        if query.query_context:
            prompt_parts.append("CONTEXT:")
            for key, value in query.query_context.items():
                prompt_parts.append(f"- {key}: {value}")

        # Finalize with instructions
        prompt_parts.append("\nPlease provide a comprehensive legal answer based on all the information above. Include:")
        prompt_parts.append("1. Analysis of the legal situation")
        prompt_parts.append("2. Relevant laws and precedents")
        prompt_parts.append("3. Practical recommendations")
        prompt_parts.append("4. Any important caveats or considerations")

        # Join all parts
        return "\n\n".join(prompt_parts)

    def _extract_legal_references(self, text: str) -> List[Dict[str, str]]:
        """
        Extract legal references from text with enhanced patterns.

        Args:
            text: Text to extract references from

        Returns:
            List of extracted legal references
        """
        # Enhanced patterns for various legal codes and case citations
        patterns = [
            # Indian legal codes - more comprehensive
            r'(?:BNS|BNSS|BSA|IPC|CrPC|CPC|PMLA|NDPS|Prevention of Corruption Act|Companies Act)\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:BNS|BNSS|BSA|IPC|CrPC|CPC|PMLA|NDPS)',
            r'(?:Article\s+)?(\d+[A-Z]?)\s+of\s+(?:the\s+)?Constitution',
            
            # Case citations - Indian format
            r'([A-Za-z\s&\.]+)\s+v[s]?\.?\s+([A-Za-z\s&\.]+),?\s+(?:\()?(\d{4})\)?\s+(\d+)\s+([A-Z]+)\s+(\d+)',
            r'(\d{4})\s+([A-Z]{2,6})\s+(\d+)',
            r'AIR\s+(\d{4})\s+([A-Z]+)\s+(\d+)',
            
            # Legal codes without section
            r'\b(?:BNS|BNSS|BSA|IPC|CrPC|CPC|PMLA|NDPS)\b',
        ]

        # Enhanced URL pattern to catch more variations
        url_pattern = r'https?://[^\s)"\'\]\}]+'

        references = []

        # Extract legal citations with better categorization
        for pattern in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                full_match = match.group(0).strip()
                
                # Categorize the reference
                ref_type = "legal_code"
                if "article" in full_match.lower():
                    ref_type = "constitutional"
                elif " v" in full_match.lower() or "air" in full_match.lower():
                    ref_type = "case_law"
                
                ref = {
                    "citation": full_match,
                    "type": ref_type
                }
                
                # Avoid duplicates
                if not any(r.get('citation') == full_match for r in references):
                    references.append(ref)

        # Extract URLs with better handling
        url_matches = re.finditer(url_pattern, text)
        for match in url_matches:
            url = match.group(0).strip()
            
            # Clean up URL (remove trailing punctuation)
            url = re.sub(r'[.,;:!?)]+$', '', url)
            
            ref = {
                "citation": url,
                "url": url,
                "type": "online_source"
            }

            # Check if this URL is already in references
            if not any(r.get('url') == url for r in references):
                references.append(ref)

        return references

    def _generate_search_queries(self, query: LegalResearchQuery) -> List[str]:
        """
        Generate a single optimized search query to avoid repetition.

        Args:
            query: The legal research query

        Returns:
            List containing single optimized search query
        """
        base_query = query.query_text

        # Return only the base query to ensure Google search runs only once
        return [base_query]

    def _generate_human_like_response(self, query: LegalResearchQuery, full_prompt: str, search_data: Dict[str, Any]) -> str:
        """
        Generate a more human-like, empathetic response with detailed source attribution.
        
        Args:
            query: The legal research query
            full_prompt: Full context prompt
            search_data: Search results data
            
        Returns:
            Human-like response string with clear source attribution
        """
        # Format search results for the prompt with detailed source tracking
        search_context = ""
        if search_data.get("combined_results"):
            print(f"\n📋 FORMATTING SEARCH RESULTS FOR AI ANALYSIS:")
            print("="*50)
            
            search_context = "🔍 **COMPREHENSIVE RESEARCH FINDINGS FROM MULTIPLE SOURCES:**\n\n"
            
            # Group results by search engine for better organization
            tavily_results = [r for r in search_data["combined_results"] if r.get("source") == "Tavily"]
            google_results = [r for r in search_data["combined_results"] if r.get("source") == "Google"]

            # Add Tavily results
            if tavily_results:
                search_context += "🟡 **TAVILY SEARCH ENGINE RESULTS:**\n"
                for i, result in enumerate(tavily_results[:5], 1):
                    title = result.get("title", "No title")
                    content = result.get("content", "")
                    url = result.get("url", "")
                    rank = result.get("search_rank", i)

                    search_context += f"**Tavily-{rank}. {title}**\n"
                    search_context += f"Content: {content}\n"
                    if url:
                        search_context += f"🔗 Tavily Source: {url}\n"
                    search_context += "\n---\n"

                    print(f"   ✅ Tavily Result {rank} formatted for AI")
                search_context += "\n"

            # Add Google results with comprehensive data
            if google_results:
                search_context += "🌐 **GOOGLE SEARCH ENGINE RESULTS (COMPREHENSIVE DATA):**\n"
                for i, result in enumerate(google_results[:10], 1):  # Show up to 10 Google results
                    title = result.get("title", "No title")
                    content = result.get("content", "")
                    snippet = result.get("snippet", "")
                    url = result.get("url", "")
                    rank = result.get("search_rank", i)

                    search_context += f"**Google-{rank}. {title}**\n"
                    if snippet and snippet != content:
                        search_context += f"Summary: {snippet}\n"
                    search_context += f"Full Content: {content}\n"
                    if url:
                        search_context += f"🔗 Google Source: {url}\n"
                    search_context += "\n---\n"

                    print(f"   ✅ Google Result {rank} formatted for AI with comprehensive data")
                search_context += "\n"
            
            print("="*50)
        
        # Create comprehensive links summary by source
        links_by_source = {
            'tavily': [],
            'google': []
        }

        for result in search_data.get("combined_results", []):
            source = result.get("source", "").lower()
            url = result.get("url", "")
            if url and source in links_by_source:
                links_by_source[source].append(url)

        links_summary = ""
        if any(links_by_source.values()):
            links_summary = "\n**📚 LINKS CATEGORIZED BY SOURCE:**\n"
            if links_by_source['tavily']:
                links_summary += f"🟡 Tavily Links: {', '.join(links_by_source['tavily'][:5])}\n"
            if links_by_source['google']:
                links_summary += f"🌐 Google Links (10+ comprehensive results): {', '.join(links_by_source['google'][:10])}\n"
        
        # Create enhanced prompt for human-like response
        enhanced_prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content=self._get_enhanced_system_prompt()),
            HumanMessage(content=f"""
            {full_prompt}

            {search_context}

            {links_summary}

            **SEARCH ENGINE SUMMARY:**
            - Tavily Engine: {len([r for r in search_data.get("combined_results", []) if r.get("source") == "Tavily"])} results
            - Google Engine: {len([r for r in search_data.get("combined_results", []) if r.get("source") == "Google"])} comprehensive results with detailed data
            - Total Unique Links: {len(search_data.get("links", []))}

            Based on this comprehensive research from Tavily and Google (single search run), provide a detailed legal answer that:
            1. References specific sources (mention if information came from Tavily or Google)
            2. Maintains an empathetic, human-like tone
            3. Includes practical guidance with legal accuracy
            4. Properly cites sources and includes all relevant links from Google's comprehensive results
            5. Utilizes the detailed content and data extracted from Google search results
            """)
        ])

        # Generate the response
        print(f"\n🤖 GENERATING AI RESPONSE WITH MULTI-SOURCE DATA...")
        logger.info("🤖 Generating human-like AI response with source attribution...")
        chain = enhanced_prompt | self.llm | StrOutputParser()
        response_content = chain.invoke({})
        
        return response_content

    def _extract_enhanced_references(self, text: str, search_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        Extract enhanced legal references with links and metadata.
        
        Args:
            text: Response text
            search_data: Search results data
            
        Returns:
            Enhanced list of references with links
        """
        references = []
        
        # Extract traditional legal references
        traditional_refs = self._extract_legal_references(text)
        references.extend(traditional_refs)
        
        # Add search result links with metadata
        for result in search_data.get("combined_results", []):
            url = result.get("url", "")
            title = result.get("title", "")
            source = result.get("source", "")
            
            if url and url not in [ref.get("url", "") for ref in references]:
                references.append({
                    "citation": f"{title} ({source})",
                    "url": url,
                    "source": source,
                    "type": "online_resource",
                    "accessed": datetime.now().strftime("%Y-%m-%d")
                })
        
        # Add direct links
        for link in search_data.get("links", []):
            if link not in [ref.get("url", "") for ref in references]:
                references.append({
                    "citation": link,
                    "url": link,
                    "type": "direct_link",
                    "accessed": datetime.now().strftime("%Y-%m-%d")
                })
        
        return references

    def _generate_contextual_follow_ups(self, query: LegalResearchQuery, answer: str) -> List[str]:
        """
        Generate more contextual and relevant follow-up suggestions.
        
        Args:
            query: Original query
            answer: Generated answer
            
        Returns:
            List of contextual follow-up questions
        """
        logger.info("💡 Generating contextual follow-up suggestions...")

        try:
            # Enhanced prompt for better follow-up generation
            followup_prompt = ChatPromptTemplate.from_messages([
                SystemMessage(content="""You are a thoughtful legal advisor generating practical follow-up questions.
                Create 5 specific, actionable follow-up questions that would genuinely help the person understand their legal situation better.
                
                Focus on:
                - Practical next steps they should consider
                - Important deadlines or timelines
                - Evidence or documentation they might need
                - Alternative approaches or options
                - Potential complications or considerations
                
                Make the questions conversational and helpful, not robotic.
                
                Return ONLY a JSON array: ["Question 1?", "Question 2?", "Question 3?", "Question 4?", "Question 5?"]"""),
                HumanMessage(content=f"""
                ORIGINAL QUESTION: {query.query_text}

                CONTEXT FROM ANSWERS:
                {query.follow_up_info if query.follow_up_info else "No additional context provided"}

                MY RESPONSE SUMMARY:
                {answer[:800]}...
                
                Generate 5 practical, specific follow-up questions that would help this person next.
                """)
            ])

            chain = followup_prompt | self.llm | JsonOutputParser()
            follow_up_questions = chain.invoke({})

            if not isinstance(follow_up_questions, list) or len(follow_up_questions) == 0:
                # Contextual fallback based on the query type
                if "bns" in query.query_text.lower() or "ipc" in query.query_text.lower():
                    follow_up_questions = [
                        "What are the practical implications of these legal changes for my specific situation?",
                        "How do the new provisions compare to the old laws in terms of penalties?",
                        "Are there any transitional provisions I should be aware of?",
                        "What documentation do I need to ensure compliance with the new laws?",
                        "How might courts interpret these new provisions in practice?"
                    ]
                else:
                    follow_up_questions = [
                        "What specific documents or evidence should I gather for my case?",
                        "What are the potential timelines and deadlines I need to be aware of?",
                        "Are there any alternative legal strategies I should consider?",
                        "What are the likely costs and practical implications of proceeding?",
                        "How strong is my legal position based on current precedents?"
                    ]

            return follow_up_questions[:5]

        except Exception as e:
            logger.error(f"❌ Error generating follow-up questions: {e}")
            return [
                "What documents or evidence would strengthen my position in this matter?",
                "What are the key deadlines or timelines I should be aware of?",
                "Are there any alternative approaches or legal strategies worth considering?",
                "What are the potential costs and practical implications of proceeding?",
                "How might recent legal developments affect my specific situation?"
            ]

    def _get_enhanced_system_prompt(self) -> str:
        """
        Get an enhanced system prompt for more human-like responses.

        Returns:
            Enhanced system prompt string
        """
        return """You are ChainVerdict, a knowledgeable and empathetic legal research assistant specializing in Indian law. You combine deep legal expertise with genuine human understanding.

🎯 **YOUR PERSONALITY:**
- Warm, approachable, and genuinely helpful
- Speaks like a trusted legal advisor, not a robot
- Balances professionalism with accessibility
- Shows empathy for people's legal concerns
- Uses clear, jargon-free language when possible

📚 **YOUR EXPERTISE:**
- Expert knowledge of Indian legal system (BNS, BNSS, BSA, IPC, CrPC, Constitution)
- Current awareness of legal developments and recent cases
- Practical understanding of legal procedures and implications
- Ability to translate complex legal concepts into understandable advice

💬 **YOUR COMMUNICATION STYLE:**
- Start responses warmly and acknowledge the person's situation
- Use conversational language while maintaining legal accuracy
- Break down complex legal concepts into digestible parts
- Provide both legal analysis AND practical guidance
- Include relevant examples or analogies when helpful
- End with encouragement and clear next steps

🔍 **YOUR APPROACH:**
- Always cite current, relevant sources and provide links
- Acknowledge limitations and recommend professional consultation when appropriate
- Consider the human impact of legal situations
- Provide multiple perspectives when laws can be interpreted differently
- Focus on actionable advice alongside theoretical knowledge

Remember: People come to you during stressful times. Be the legal advisor you'd want to consult - knowledgeable, trustworthy, and genuinely caring about their wellbeing.

**CRITICAL:** Always include relevant links, cite your sources properly, and make your legal analysis both accurate and accessible."""

    def clear_conversation(self, conversation_id: str) -> bool:
        """
        Clear a conversation from the store.

        Args:
            conversation_id: ID of the conversation to clear

        Returns:
            True if cleared, False if not found
        """
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            return True
        return False


class LegalResearch:
    """Main class for the Legal Research framework."""

    def __init__(self):
        """Initialize the Legal Research framework."""
        try:
            self.agent = LegalResearchAgent()
            logger.info("Successfully initialized Legal Research Agent")
        except Exception as e:
            logger.error(f"Failed to initialize Legal Research Agent: {e}")
            raise ValueError(f"Could not initialize Legal Research framework: {e}")

    def process_query(self, query: str, follow_up_info: Optional[Dict[str, Any]] = None, conversation_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a legal research query.

        Args:
            query: The query string
            follow_up_info: Additional information provided by the user
            conversation_id: ID to track the conversation

        Returns:
            Response dictionary
        """
        # Create a query object
        query_obj = LegalResearchQuery(
            query_text=query,
            conversation_id=conversation_id
        )

        # Process the query
        response = self.agent.process_query(query_obj, follow_up_info)

        # Convert to dictionary for API response
        return {
            "is_complete": response.is_complete,
            "current_question": response.current_question,
            "question_number": response.question_number,
            "total_questions": response.total_questions,
            "answer": response.answer,
            "references": response.references,
            "follow_up_suggestions": response.follow_up_suggestions,
            "conversation_id": response.conversation_id
        }

    def clear_conversation(self, conversation_id: str) -> bool:
        """
        Clear a conversation from the store.

        Args:
            conversation_id: ID of the conversation to clear

        Returns:
            True if cleared, False if not found
        """
        return self.agent.clear_conversation(conversation_id)


# Enhanced example usage for testing the Legal Research framework
def test_enhanced_legal_research():
    """Test the Enhanced Legal Research framework with comprehensive search capabilities."""
    print("🚀 Starting Enhanced Legal Research Demo...")
    print("="*60)
    
    legal_research = LegalResearch()

    # Example query
    query = "What are the key differences between BNS and IPC regarding cybercrime provisions?"

    print(f"📋 Testing Legal Research with query:\n'{query}'")
    print("="*60)

    # Start the conversation
    response = legal_research.process_query(query)
    conversation_id = response.get('conversation_id')

    # Check if clarification is needed
    if not response.get("is_complete", False):
        print("\n❓ CLARIFICATION PHASE:")
        print(f"Question {response.get('question_number', 1)}/{response.get('total_questions', 3)}: {response.get('current_question')}")
        print("(In a real application, users would provide detailed answers)")

        # Simulate progressive answers for testing
        test_answers = [
            "I'm particularly interested in online fraud, hacking, and digital evidence provisions",
            "I need this for a comparative analysis and to understand practical implications for law enforcement",
            "I want to understand both the theoretical changes and practical enforcement differences"
        ]

        # Process all clarification questions
        for i, answer in enumerate(test_answers):
            if not response.get("is_complete", False):
                print(f"\n💬 Simulated Answer {i+1}: {answer}")
                
        follow_up_info = {
                    str(response.get('question_number', 1) - 1): answer
        }

        response = legal_research.process_query(
            query,
            follow_up_info=follow_up_info,
            conversation_id=conversation_id
        )

        if not response.get("is_complete", False):
                    print(f"❓ Next Question {response.get('question_number', 1)}/{response.get('total_questions', 3)}: {response.get('current_question')}")

    # Print comprehensive results
    print("\n" + "="*60)
    print("🎯 COMPREHENSIVE LEGAL ANALYSIS:")
    print("="*60)
    print(response.get("answer", "No answer generated"))

    # Print enhanced references with categories AND search engine sources
    references = response.get("references", [])
    if references:
        print("\n📚 REFERENCES AND SOURCES BY SEARCH ENGINE:")
        print("-"*60)
        
        # Group references by search engine source first, then by type
        search_engine_refs = {
            'Tavily': [],
            'Google': [],
            'Legal_Citations': [],
            'Other': []
        }

        for ref in references:
            source = ref.get('source', '')
            ref_type = ref.get('type', 'other')

            # Categorize by search engine
            if source == 'Tavily':
                search_engine_refs['Tavily'].append(ref)
            elif source == 'Google':
                search_engine_refs['Google'].append(ref)
            elif ref_type in ['legal_code', 'case_law', 'constitutional']:
                search_engine_refs['Legal_Citations'].append(ref)
            else:
                search_engine_refs['Other'].append(ref)

        # Display by search engine
        for engine, refs in search_engine_refs.items():
            if refs:
                if engine == 'Tavily':
                    print(f"\n🟡 TAVILY SEARCH ENGINE ({len(refs)} sources):")
                elif engine == 'Google':
                    print(f"\n🌐 GOOGLE SEARCH ENGINE ({len(refs)} comprehensive sources):")
                elif engine == 'Legal_Citations':
                    print(f"\n⚖️ LEGAL CITATIONS EXTRACTED ({len(refs)} citations):")
                else:
                    print(f"\n📄 OTHER SOURCES ({len(refs)} sources):")

                # Show more results for Google since we get 10+ results
                max_results = 10 if engine == 'Google' else 5
                for i, ref in enumerate(refs[:max_results], 1):
                    citation = ref.get('citation', 'No citation')
                    url = ref.get('url', '')
                    ref_type = ref.get('type', 'unknown')
                    accessed = ref.get('accessed', '')

                    print(f"   {i}. {citation[:80]}..." if len(citation) > 80 else f"   {i}. {citation}")

                    if url:
                        print(f"      🔗 Link: {url}")
                    if ref_type != 'unknown':
                        print(f"      📋 Type: {ref_type.replace('_', ' ').title()}")
                    if accessed:
                        print(f"      📅 Accessed: {accessed}")
                    print()

    # Print contextual follow-up suggestions
    follow_ups = response.get("follow_up_suggestions", [])
    if follow_ups:
        print(f"\n💡 SUGGESTED FOLLOW-UP QUESTIONS:")
        print("-"*40)
        for i, question in enumerate(follow_ups, 1):
            print(f"{i}. {question}")

    # Print search engine performance summary
    if hasattr(legal_research.agent, 'search_manager'):
        search_engines = legal_research.agent.search_manager.search_engines
        print(f"\n🔧 SEARCH ENGINE STATUS SUMMARY:")
        print("-"*40)
        print(f"🟡 Tavily: {'✅ Active' if 'tavily' in search_engines else '❌ Inactive'}")
        print(f"🌐 Google: {'✅ Active (10+ results)' if 'google' in search_engines else '❌ Inactive'}")
        print(f"📊 Total Active Engines: {len(search_engines)}/2")

    print("\n" + "="*60)
    print("✅ Enhanced Legal Research Demo Complete!")
    print("🔍 Tavily and Google (single comprehensive search) provided detailed results!")
    print("="*60)

# Alternative simple test for quick verification
def quick_test():
    """Quick test with minimal output for verification and search engine status."""
    try:
        legal_research = LegalResearch()
        
        # Test basic functionality
        response = legal_research.process_query("What is Section 124A of IPC about?")
        
        # Check search engine status
        search_engines = legal_research.agent.search_manager.search_engines
        active_engines = list(search_engines.keys())
        
        if response.get("is_complete"):
            print("✅ Legal Research system working correctly!")
            print(f"📄 Sample answer length: {len(response.get('answer', ''))} characters")
            print(f"📚 References found: {len(response.get('references', []))}")
            print(f"💡 Follow-ups generated: {len(response.get('follow_up_suggestions', []))}")
            print(f"🔍 Active search engines: {', '.join(active_engines)} ({len(active_engines)}/2)")

            # Show which engines found results
            refs = response.get('references', [])
            if refs:
                tavily_count = len([r for r in refs if r.get('source') == 'Tavily'])
                google_count = len([r for r in refs if r.get('source') == 'Google'])
                print(f"📊 Results breakdown: Tavily({tavily_count}) + Google({google_count} comprehensive)")
        else:
            print("⚠️ System requires clarification (normal behavior)")
            print(f"❓ Current question: {response.get('current_question', 'None')}")
            print(f"🔍 Active search engines: {', '.join(active_engines)} ({len(active_engines)}/2)")
        
        return True
    except Exception as e:
        print(f"❌ Error in system: {e}")
        return False

# Test function for the fixed search engines
def test_fixed_search_engines():
    """Test the fixed search engines with a specific legal query."""
    print("🧪 TESTING FIXED SEARCH ENGINES")
    print("="*60)
    
    try:
        legal_research = LegalResearch()
        
        # Test with a specific query about new laws
        query = "Section 377 BNS vs IPC homosexuality decriminalization"
        print(f"📋 Testing with query: '{query}'")
        print("-"*60)
        
        response = legal_research.process_query(query)
        
        if response.get("is_complete"):
            print("✅ Query processed completely without clarification!")
            print(f"📄 Answer preview: {response.get('answer', '')[:200]}...")
            
            refs = response.get('references', [])
            print(f"\n📚 Found {len(refs)} references:")
            
            # Show breakdown by search engine
            tavily_count = len([r for r in refs if r.get('source') == 'Tavily'])
            google_count = len([r for r in refs if r.get('source') == 'Google'])

            print(f"   🟡 Tavily: {tavily_count} results")
            print(f"   🌐 Google: {google_count} comprehensive results")
            
            # Show some actual links
            links = [r.get('url') for r in refs if r.get('url')]
            print(f"\n🔗 Sample links found: {len(links)}")
            for i, link in enumerate(links[:3], 1):
                print(f"   {i}. {link}")
                
        else:
            print("⚠️ Query requires clarification (testing with sample answer)")
            # Provide a sample answer
            sample_answer = "I want to understand the legal changes regarding LGBTQ+ rights and privacy laws."
            follow_up = {str(response.get('question_number', 1) - 1): sample_answer}
            
            response = legal_research.process_query(
                query,
                follow_up_info=follow_up,
                conversation_id=response.get('conversation_id')
            )
            
            if response.get("is_complete"):
                print("✅ Query completed after clarification!")
                refs = response.get('references', [])
                print(f"📚 Found {len(refs)} references after clarification")
        
        print("\n" + "="*60)
        print("🎯 OPTIMIZED SEARCH ENGINES TEST COMPLETE!")
        print("🔍 DuckDuckGo removed, Google provides 10+ comprehensive results!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

# Alternative simple legal query test
def test_simple_legal_query():
    """Test with a simple legal query that should work quickly."""
    print("\n🔬 SIMPLE LEGAL QUERY TEST")
    print("="*40)
    
    try:
        legal_research = LegalResearch()
        
        query = "IPC Section 420 cheating punishment"
        print(f"📋 Query: {query}")
        
        response = legal_research.process_query(query)
        
        if response.get("is_complete"):
            answer = response.get('answer', '')
            print(f"✅ Got answer ({len(answer)} chars)")
            
            refs = response.get('references', [])
            print(f"📚 References: {len(refs)}")
            
            links = [r.get('url') for r in refs if r.get('url')]
            print(f"🔗 Links: {len(links)}")
            
            return True
        else:
            print(f"⚠️ Needs clarification: {response.get('current_question')}")
            return True
            
    except Exception as e:
        print(f"❌ Simple test failed: {e}")
        return False

# Main execution - enhanced version with fixed search engines
if __name__ == "__main__":
    print("🔍 CHAINVERDICT LEGAL RESEARCH - ENHANCED & FIXED VERSION")
    print("="*60)
    print("📊 Checking API Availability:")
    print(f"   • OpenAI API Key: {'✅ Available' if OPENAI_API_KEY else '❌ Missing'}")
    print(f"   • Gemini API Key: {'✅ Available' if GEMINI_API_KEY else '❌ Missing'}")
    print(f"   • Tavily API Key: {'✅ Available' if TAVILY_API_KEY else '❌ Missing'}")
    print(f"   • Google API Key: {'✅ Available' if GOOGLE_API_KEY else '❌ Missing'}")
    print(f"   • Google CSE ID: {'✅ Available' if GOOGLE_CSE_ID else '❌ Missing'}")
    print("="*60)
    
    # Run quick test first
    print("\n🧪 Running Quick System Test...")
    if quick_test():
        print("\n🔬 Running Simple Legal Query Test...")
        if test_simple_legal_query():
            print("\n🚀 Running Fixed Search Engines Test...")
            test_fixed_search_engines()
        else:
            print("❌ Simple test failed. Skipping advanced tests.")
    else:
        print("❌ System failed quick test. Please check your API keys and dependencies.")
        print("\n🔧 Trying simple legal query test anyway...")
        test_simple_legal_query()
