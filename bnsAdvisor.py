"""
BNSAdvisor: One-Stop Legal Solutions

An agentic AI framework for legal analysis and advice, specializing in Indian law
with particular focus on the Bharatiya Nyaya Sanhita (BNS), Bharatiya Nagarik <PERSON>aks<PERSON> (BNSS), and Bharatiya Sakshya <PERSON>m (BSA).

This framework provides:
1. Legal case analysis based on user input
2. Section mapping between old and new legal codes
3. Procedural advice for legal proceedings
4. Evidence analysis and recommendations
5. Multi-agent architecture for comprehensive legal solutions
"""

import os
import logging
from typing import Dict, List, Any, Optional, Union
import asyncio
import re
import time
import sys
import threading

# LangChain imports
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.runnables import Runnable, RunnableConfig
from langchain_core.output_parsers import StrOutputParser, JsonOutputParser
from langchain_core.tools import BaseTool, StructuredTool, Tool

# LLM providers
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI


# Search tools
from langchain_tavily import TavilySearch
from langchain_community.tools import DuckDuckGoSearchResults
from langchain_google_community import GoogleSearchAPIWrapper

# Agent frameworks
from langgraph.graph import END, StateGraph
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver

# Configure logging - only show errors
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("BNSAdvisor")

# Hardcoded API keys - no more environment variables
GEMINI_API_KEY = "AIzaSyCrPNxDrDvmYHPINVA1rTJiLUoOL-vjQZE"
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
TAVILY_API_KEY = "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"
GOOGLE_API_KEY = "AIzaSyAX0FikJZQ720Aw5Aj0qvVMVEqZWenR-rg"
GOOGLE_CSE_ID = "72373415d67ee4bb9"
LANGCHAIN_API_KEY = os.getenv("LANGCHAIN_API_KEY")

# print(GEMINI_API_KEY, OPENAI_API_KEY, TAVILY_API_KEY, GOOGLE_API_KEY, GOOGLE_CSE_ID, LANGCHAIN_API_KEY)

# Set environment variables explicitly for any libraries that still look for them
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY or ""
os.environ["GOOGLE_CSE_ID"] = GOOGLE_CSE_ID or ""
os.environ["TAVILY_API_KEY"] = TAVILY_API_KEY or ""

INDIAN_LEGAL_WEBSITES = [
    "indiankanoon.org",
    "sci.gov.in",
    "delhihighcourt.nic.in",
    "bombayhighcourt.nic.in",
    "mhc.tn.gov.in",
    "hcraj.nic.in",
    "lawcommissionofindia.nic.in",
    "meity.gov.in",
    "cic.gov.in",
    "nhrc.nic.in",
    "advocatekhoj.com",
    "lawyersclubindia.com",
    "manupatra.com",
    "scconline.com"
]


class LegalQuery:
    """Class for legal queries submitted to the system."""
    
    def __init__(self, query_text, language="english", query_type="general", jurisdiction="india"):
        """
        Initialize a legal query.
        
        Args:
            query_text: The legal query or case description
            language: Language of the query (english, hindi, etc.)
            query_type: Type of legal query (criminal, civil, property, etc.)
            jurisdiction: Legal jurisdiction (india, international, etc.)
        """
        self.query_text = query_text
        self.language = language
        self.query_type = query_type
        self.jurisdiction = jurisdiction


class LegalReference:
    """Class for legal references and citations."""
    
    def __init__(self, code, section, description, punishment=None, url=None):
        """
        Initialize a legal reference.
        
        Args:
            code: Legal code (BNS, BNSS, BSA, IPC, CrPC, etc.)
            section: Section number or identifier
            description: Brief description of the section
            punishment: Associated punishment if applicable
            url: URL to official documentation
        """
        self.code = code
        self.section = section
        self.description = description
        self.punishment = punishment
        self.url = url


class LegalResponse:
    """Class for structured legal responses."""
    
    def __init__(self, summary, applicable_sections=None, procedural_advice=None, 
                 evidence_requirements=None, next_steps=None, references=None):
        """
        Initialize a legal response.
        
        Args:
            summary: Brief summary of the legal analysis
            applicable_sections: List of applicable legal sections
            procedural_advice: Advice on legal procedures
            evidence_requirements: Required evidence
            next_steps: Recommended next steps
            references: Reference materials and citations
        """
        self.summary = summary
        self.applicable_sections = applicable_sections or []
        self.procedural_advice = procedural_advice
        self.evidence_requirements = evidence_requirements
        self.next_steps = next_steps or []
        self.references = references or []


class LegalVerifier:
    """Verifies legal references with supporting evidence from search engines."""
    
    def __init__(self, search_tool=None):
        """
        Initialize the legal verifier.
        
        Args:
            search_tool: Search tool to use for finding supporting evidence
        """
        self.search_tool = search_tool
        if not self.search_tool:
            try:
                # Try to create Google search if not provided
                self.search_tool = GoogleSearchAPIWrapper(
                    google_api_key=GOOGLE_API_KEY, 
                    google_cse_id=GOOGLE_CSE_ID
                )
            except Exception as e:
                logger.warning(f"Could not initialize Google search for verification: {e}")
                self.search_tool = None
    
    def extract_legal_references(self, text):
        """
        Extract legal references from text with enhanced accuracy for BNS, BNSS, BSA.
        
        Args:
            text: Text to extract references from
            
        Returns:
            List of extracted legal references
        """
        # Enhanced patterns for NEW CODES (BNS, BNSS, BSA) with better accuracy
        patterns = [
            # BNS patterns - Primary
            r'BNS\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'BNS\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'Bharatiya\s+Nyaya\s+Sanhita\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:BNS|Bharatiya\s+Nyaya\s+Sanhita)',
            
            # BNSS patterns - Primary
            r'BNSS\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'BNSS\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'Bharatiya\s+Nagarik\s+Suraksha\s+Sanhita\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:BNSS|Bharatiya\s+Nagarik\s+Suraksha\s+Sanhita)',
            
            # BSA patterns - Primary
            r'BSA\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'BSA\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'Bharatiya\s+Sakshya\s+Adhiniyam\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:BSA|Bharatiya\s+Sakshya\s+Adhiniyam)',
            
            # Short form references
            r'[Ss]\.\s*(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:BNS|BNSS|BSA)',
            
            # Old codes for reference mapping (but marked as legacy)
            r'IPC\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'CrPC\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'Evidence\s+Act\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
        ]
        
        references = []
        for pattern in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                # Extract the full match and the section number
                full_match = match.group(0)
                section_num = match.group(1)
                
                # Determine which code - prioritize NEW CODES
                code = "Unknown"
                code_type = "unknown"  # new, legacy, or other
                
                full_match_lower = full_match.lower()
                
                # NEW CODES (Priority)
                if any(term in full_match_lower for term in ["bns", "bharatiya nyaya sanhita"]):
                    code = "BNS"
                    code_type = "new"
                elif any(term in full_match_lower for term in ["bnss", "bharatiya nagarik suraksha sanhita"]):
                    code = "BNSS"
                    code_type = "new"
                elif any(term in full_match_lower for term in ["bsa", "bharatiya sakshya adhiniyam"]):
                    code = "BSA"
                    code_type = "new"
                # LEGACY CODES (For reference only)
                elif any(term in full_match_lower for term in ["ipc", "indian penal code"]):
                    code = "IPC"
                    code_type = "legacy"
                elif any(term in full_match_lower for term in ["crpc", "criminal procedure code"]):
                    code = "CrPC"
                    code_type = "legacy"
                elif any(term in full_match_lower for term in ["evidence act"]):
                    code = "Evidence Act"
                    code_type = "legacy"
                
                # Add to references if not already present
                ref = {
                    "code": code, 
                    "section": section_num, 
                    "full_match": full_match,
                    "code_type": code_type
                }
                if ref not in references:
                    references.append(ref)
        
        return references
    
    def find_supporting_evidence(self, reference):
        """
        Find supporting evidence for a legal reference with enhanced accuracy for law enforcement.
        
        Args:
            reference: Legal reference to find evidence for
            
        Returns:
            List of supporting evidence
        """
        if not self.search_tool:
            logger.warning("Search tool unavailable for finding supporting evidence")
            return []
        
        try:
            # Construct enhanced search query for law enforcement
            code = reference["code"]
            section = reference["section"]
            
            # Get section details for better search
            section_details = self.get_section_details(code, section)
            section_name = section_details.get("name", "")
            
            # Create comprehensive search query
            if code in ["BNS", "BNSS", "BSA"]:
                query = f"{code} Section {section} {section_name} Indian criminal law police procedure"
                
                # Add specific terms for better results
                if code == "BNS":
                    query += " Bharatiya Nyaya Sanhita 2023 criminal offense"
                elif code == "BNSS":
                    query += " Bharatiya Nagarik Suraksha Sanhita 2023 police procedure"
                elif code == "BSA":
                    query += " Bharatiya Sakshya Adhiniyam 2023 evidence law"
            else:
                query = f"{code} Section {section} Indian legal code reference"
            
            # Perform search
            search_results = self.search_tool.run(query)
            
            # Parse results
            evidence = []
            if isinstance(search_results, str):
                # Extract URLs from text results
                urls = re.findall(r'https?://[^\s]+', search_results)
                titles = re.findall(r'(?:^|\n)([^.\n]+)(?=\.)', search_results)
                
                for i, url in enumerate(urls[:5]):  # Limit to top 5 for better quality
                    title = titles[i] if i < len(titles) else f"{code} Section {section} - {section_name}"
                    if title and url and "error" not in title.lower():
                        evidence.append({
                            "title": title,
                            "link": url,
                            "section_details": section_details
                        })
            else:
                # Handle structured results
                for item in search_results[:5]:  # Limit to top 5
                    title = item.get("title", "")
                    link = item.get("link", "")
                    if title and link and "error" not in title.lower():
                        evidence.append({
                            "title": title,
                            "link": link,
                            "section_details": section_details
                        })
            
            return evidence
        except Exception as e:
            logger.error(f"Error finding supporting evidence for {reference.get('code', 'unknown')} Section {reference.get('section', 'unknown')}: {e}")
            return []

    def verify_analysis(self, analysis_text):
        """
        Verify legal analysis with supporting evidence.
        
        Args:
            analysis_text: Legal analysis text to verify
            
        Returns:
            Original text with supporting evidence
        """
        # Extract references
        references = self.extract_legal_references(analysis_text)
        
        if not references:
            return analysis_text
        
        # Find supporting evidence for each reference
        verification_text = "\n\n## Supporting Evidence\n"
        for ref in references:
            code = ref["code"]
            section = ref["section"]
            
            evidence = self.find_supporting_evidence(ref)
            for item in evidence:
                verification_text += f"- {code} {section}: [{item['title']}]({item['link']})\n"
        
        # Combine original analysis with verification
        return analysis_text + verification_text


class Spinner:
    """A simple spinner class to show loading animation in terminal."""
    
    def __init__(self, message="Thinking"):
        self.spinner_chars = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
        self.message = message
        self.running = False
        self.spinner_thread = None

    def spin(self):
        while self.running:
            for char in self.spinner_chars:
                if not self.running:
                    break
                sys.stdout.write(f'\r{char} {self.message}...')
                sys.stdout.flush()
                time.sleep(0.1)

    def start(self):
        self.running = True
        self.spinner_thread = threading.Thread(target=self.spin)
        self.spinner_thread.start()

    def stop(self):
        self.running = False
        if self.spinner_thread:
            self.spinner_thread.join()
        sys.stdout.write('\r' + ' ' * (len(self.message) + 15) + '\r')
        sys.stdout.flush()


class LLMProvider:
    """Interface for different LLM providers."""
    
    @staticmethod
    def get_llm(provider: str = "gemini", model: Optional[str] = None, temperature: float = 0) -> Runnable:
        """Factory method to get an LLM based on provider."""
        try:
            logger.info(f"Initializing {provider} LLM model")
            spinner = Spinner(f"Initializing {provider} model")
            spinner.start()
            
            if provider == "gemini":
                model_name = model or "gemini-2.5-pro"
                logger.info(f"Using Gemini model: {model_name}")
                
                # Create a wrapper class to handle the spinner
                class SpinnerWrappedGemini(ChatGoogleGenerativeAI):
                    def __call__(self, *args, **kwargs):
                        spinner = Spinner("Processing with Gemini")
                        spinner.start()
                        try:
                            result = super().__call__(*args, **kwargs)
                            return result
                        finally:
                            spinner.stop()
                
                llm = SpinnerWrappedGemini(
                    model=model_name,
                    api_key=GEMINI_API_KEY,
                    temperature=temperature,
                    request_timeout=30
                )
                spinner.stop()
                return llm
            
            elif provider == "openai":
                model_name = model or "gpt-4o-mini"
                logger.info(f"Using OpenAI model: {model_name}")
                
                # Create a wrapper class to handle the spinner
                class SpinnerWrappedOpenAI(ChatOpenAI):
                    def __call__(self, *args, **kwargs):
                        spinner = Spinner("Processing with OpenAI")
                        spinner.start()
                        try:
                            result = super().__call__(*args, **kwargs)
                            return result
                        finally:
                            spinner.stop()
                
                llm = SpinnerWrappedOpenAI(
                    model=model_name,
                    api_key=OPENAI_API_KEY,
                    temperature=temperature,
                    request_timeout=30
                )
                spinner.stop()
                return llm
            
            else:
                logger.warning(f"Unknown provider {provider}, defaulting to Gemini")
                spinner.stop()
                return LLMProvider.get_llm("gemini", model, temperature)
                
        except Exception as e:
            if 'spinner' in locals():
                spinner.stop()
            error_msg = f"Error initializing LLM ({provider}): {str(e)}"
            logger.error(error_msg)
            logger.exception("Detailed exception information:")
            print(f"ERROR: {error_msg}")
            
            # Fallback to a default LLM
            try:
                logger.info("Attempting fallback to Gemini")
                return ChatGoogleGenerativeAI(
                    model="gemini-2.0-flash",
                    api_key=GEMINI_API_KEY,
                    temperature=temperature,
                    request_timeout=30
                )
            except Exception as fallback_error:
                error_msg = f"Fallback LLM initialization also failed: {str(fallback_error)}"
                logger.error(error_msg)
                raise ValueError(error_msg)


class EnhancedSearchToolFactory:
    """🔥 PREMIUM Enhanced Factory for creating advanced search tools with multi-engine capabilities."""

    @staticmethod
    def create_enhanced_tavily_search(max_results: int = 10) -> BaseTool:
        """Create an enhanced Tavily search tool with premium features."""
        print("🟡 Initializing Enhanced Tavily Search for BNS Advisor...")
        if not TAVILY_API_KEY:
            print("   ❌ Tavily API key not found")
            logger.warning("Tavily API key not found")
            return None

        try:
            # Enhanced Tavily search with premium configuration
            tavily_tool = TavilySearch(
                max_results=max_results,
                api_key=TAVILY_API_KEY,
                search_depth="advanced",
                include_answer=True,
                include_raw_content=True,
                include_images=False,  # Focus on text for legal content
                include_links=True,
                include_videos=False,  # Legal content is primarily text-based
                include_image_descriptions=False,
                include_domains=INDIAN_LEGAL_WEBSITES  # Target all Indian legal sites
            )
            print("   ✅ Enhanced Tavily search initialized with Indian legal sites focus")
            return tavily_tool
        except Exception as e:
            print(f"   ❌ Tavily initialization failed: {e}")
            logger.error(f"Failed to initialize Tavily: {e}")
            return None

    @staticmethod
    def create_enhanced_google_search(max_results: int = 10) -> BaseTool:
        """Create an enhanced Google search tool with premium features."""
        print("🌐 Initializing Enhanced Google Search for BNS Advisor...")
        if not GOOGLE_API_KEY or not GOOGLE_CSE_ID:
            missing = []
            if not GOOGLE_API_KEY:
                missing.append("GOOGLE_API_KEY")
            if not GOOGLE_CSE_ID:
                missing.append("GOOGLE_CSE_ID")
            print(f"   ❌ Google search missing: {', '.join(missing)}")
            logger.warning(f"Google API credentials not found: {missing}")
            return None

        try:
            # Enhanced Google search with premium configuration
            search = GoogleSearchAPIWrapper(
                google_api_key=GOOGLE_API_KEY,
                google_cse_id=GOOGLE_CSE_ID,
                k=max_results  # Get more results for comprehensive coverage
            )

            google_tool = Tool(
                name="enhanced_google_search",
                description="🔥 PREMIUM: Enhanced Google search for comprehensive legal information with 10+ results and Indian legal site focus.",
                func=search.run,
            )
            print("   ✅ Enhanced Google search initialized with 10+ results capability")
            return google_tool
        except Exception as e:
            print(f"   ❌ Google initialization failed: {e}")
            logger.error(f"Failed to initialize Google search: {e}")
            return None

    @staticmethod
    def get_premium_search_tools() -> List[BaseTool]:
        """Get all available premium search tools (no DuckDuckGo for stability)."""
        print("🔧 INITIALIZING PREMIUM SEARCH TOOLS FOR BNS ADVISOR...")
        print("="*60)

        tools = []

        # Enhanced Tavily search
        tavily_tool = EnhancedSearchToolFactory.create_enhanced_tavily_search(10)
        if tavily_tool:
            tools.append(tavily_tool)

        # Enhanced Google search
        google_tool = EnhancedSearchToolFactory.create_enhanced_google_search(10)
        if google_tool:
            tools.append(google_tool)

        print(f"\n📊 PREMIUM SEARCH TOOLS INITIALIZATION COMPLETE:")
        print(f"   🎯 Successfully initialized: {len(tools)}/2 premium search engines")
        print(f"   🔍 Active premium tools: {[tool.name for tool in tools]}")
        print(f"   🇮🇳 Indian legal sites targeted: {len(INDIAN_LEGAL_WEBSITES)} sites")

        if len(tools) == 0:
            print("   ⚠️ WARNING: No search engines available! Results will be limited.")
        else:
            print("   ✅ PREMIUM SEARCH ENGINES READY FOR ENHANCED BNS ANALYSIS!")

        print("="*60)
        return tools


class LegalAgentFactory:
    """Factory for creating specialized legal agents."""
    
    @staticmethod
    def create_bns_expert(llm: Optional[Runnable] = None) -> Runnable:
        """Create a BNS (Bharatiya Nyaya Sanhita) expert agent."""
        if llm is None:
            llm = LLMProvider.get_llm("gemini", "gemini-2.5-pro")
            
        system_prompt = """
        You are an expert in the Indian Judiciary System, specializing in the Bharatiya Nyaya Sanhita (BNS).
        Your role is to:
        1. Identify applicable BNS sections for given legal cases
        2. Map BNS sections to their previous IPC counterparts
        3. Explain punishments and legal implications under the BNS
        
        Use these reference resources as well:
        - BNS and IPC Comparative Table: https://www.thelawadvice.com/articles/comparative-table-of-ipc-and-bharatiya-nyaya-sanhita-2023
        - Bharatiya Nyaya Sanhita Wikipedia: https://en.wikipedia.org/wiki/Bharatiya_Nyaya_Sanhita
        """
        
        return llm

    @staticmethod
    def create_bnss_expert(llm: Optional[Runnable] = None) -> Runnable:
        """Create a BNSS (Bharatiya Nagarik Suraksha Sanhita) expert agent."""
        if llm is None:
            llm = LLMProvider.get_llm("gemini", "gemini-2.5-pro")
            
        system_prompt = """
        You are an expert in Indian procedural law, specializing in the Bharatiya Nagarik Suraksha Sanhita (BNSS).
        Your role is to:
        1. Identify applicable BNSS sections for procedural aspects of legal cases
        2. Map BNSS sections to their previous CrPC counterparts
        3. Explain procedural requirements and implications
        
        Use these reference resources:
        - BNSS and CrPC Comparative Table: https://www.thelawadvice.com/articles/comparative-table-of-crpc-and-bharatiya-nagarik-suraksha-sanhita-2023
        - Bharatiya Nagarik Suraksha Sanhita Wikipedia: https://en.wikipedia.org/wiki/Bharatiya_Nagarik_Suraksha_Sanhita
        """
        
        return llm
    
    @staticmethod
    def create_bsa_expert(llm: Optional[Runnable] = None) -> Runnable:
        """Create a BSA (Bharatiya Sakshya Adhiniyam) expert agent."""
        if llm is None:
            llm = LLMProvider.get_llm("gemini", "gemini-2.5-pro")
            
        system_prompt = """
        You are an expert in Indian evidence law, specializing in the Bharatiya Sakshya Adhiniyam (BSA).
        Your role is to:
        1. Identify applicable BSA sections for evidentiary aspects of legal cases
        2. Map BSA sections to their previous Indian Evidence Act counterparts
        3. Explain evidentiary requirements and implications
        
        Use these reference resources:
        - BSA and Evidence Act Comparative Table: https://www.thelawadvice.com/articles/comparative-table-of-bharatiya-sakshya-act-2023-and-indian-evidence-act-1872
        - Bharatiya Sakshya Adhiniyam Wikipedia: https://en.wikipedia.org/wiki/Bharatiya_Sakshya_Adhiniyam
        """
        
        return llm


class LegalAnalysisAgent:
    """Main agent that coordinates legal analysis across domains."""
    
    def __init__(self):
        """Initialize the legal analysis agent."""
        self.memory = MemorySaver()
        self.tools = self._initialize_tools()
        self.llm = LLMProvider.get_llm("gemini", "gemini-2.5-pro")
        self.agent_executor = self._initialize_agent()
        
    def _initialize_tools(self) -> List[BaseTool]:
        """Initialize and return the search and analysis tools."""
        tools = []
        
        # Add premium enhanced search tools (no DuckDuckGo for stability)
        premium_tools = EnhancedSearchToolFactory.get_premium_search_tools()
        tools.extend(premium_tools)
            
        return tools
    
    def _initialize_agent(self) -> Runnable:
        """Initialize and return the agent executor."""
        try:
            logger.info("Creating ReAct agent with tools")
            # Create a ReAct agent
            return create_react_agent(
                self.llm,
                self.tools,
                checkpointer=self.memory
            )
        except Exception as e:
            logger.error(f"Error creating ReAct agent: {e}")
            logger.info("Creating simplified agent without tools")
            
            # Fallback: Create a simpler agent without tools if the ReAct agent fails
            # This just wraps the LLM to maintain API compatibility
            def simple_agent_invoke(inputs, config=None):
                messages = inputs.get("messages", [])
                response = self.llm.invoke(messages)
                return {"messages": [response]}
            
            simple_agent = lambda: None  # Create a dummy object
            simple_agent.invoke = simple_agent_invoke
            simple_agent.stream = lambda inputs, config=None: [simple_agent_invoke(inputs, config)]
            
            return simple_agent
    
    def analyze_case(self, query: Union[str, LegalQuery], config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze a legal case and provide relevant advice.
        
        Args:
            query: A string description of the case or a LegalQuery object
            config: Optional configuration for the agent execution
            
        Returns:
            A dictionary with the agent's response
        """
        if config is None:
            config = {"configurable": {"thread_id": "legal-analysis-001"}}
            
        # Convert string query to LegalQuery if needed
        if isinstance(query, str):
            query = LegalQuery(query_text=query)
            
        # Construct the prompt
        prompt = self._construct_legal_prompt(query)
        
        # Execute the agent
        try:
            response = self.agent_executor.invoke(
                {"messages": [HumanMessage(content=prompt)]},
                config
            )
            return response
        except Exception as e:
            logger.error(f"Error executing agent: {e}")
            return {"error": str(e)}
    
    async def analyze_case_async(self, query: Union[str, LegalQuery], config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Asynchronously analyze a legal case and provide relevant advice.
        
        Args:
            query: A string description of the case or a LegalQuery object
            config: Optional configuration for the agent execution
            
        Returns:
            A dictionary with the agent's response
        """
        if config is None:
            config = {"configurable": {"thread_id": "legal-analysis-001"}}
            
        # Convert string query to LegalQuery if needed
        if isinstance(query, str):
            query = LegalQuery(query_text=query)
            
        # Construct the prompt
        prompt = self._construct_legal_prompt(query)
        
        # Execute the agent asynchronously
        try:
            response = await self.agent_executor.ainvoke(
                {"messages": [HumanMessage(content=prompt)]},
                config
            )
            return response
        except Exception as e:
            logger.error(f"Error executing agent asynchronously: {e}")
            return {"error": str(e)}
    
    def stream_analysis(self, query: Union[str, LegalQuery], config: Optional[Dict[str, Any]] = None):
        """
        Stream the analysis results as they are generated.
        
        Args:
            query: A string description of the case or a LegalQuery object
            config: Optional configuration for the agent execution
            
        Returns:
            A generator yielding chunks of the agent's response
        """
        if config is None:
            config = {"configurable": {"thread_id": "legal-analysis-001"}}
            
        # Convert string query to LegalQuery if needed
        if isinstance(query, str):
            query = LegalQuery(query_text=query)
            
        # Construct the prompt
        prompt = self._construct_legal_prompt(query)
        
        # First try: use the agent executor's invoke method
        try:
            logger.info("Using simplified streaming (synchronous with yield)")
            response = self.agent_executor.invoke(
                {"messages": [HumanMessage(content=prompt)]},
                config
            )
            # Yield the entire response as a single chunk
            yield response
            return
        except Exception as e:
            logger.error(f"Error in agent executor invoke: {e}")
            
        # Second try: direct LLM invocation as fallback
        try:
            logger.info("Falling back to direct LLM invocation")
            messages = [
                SystemMessage(content="You are a legal expert specialized in Indian law."),
                HumanMessage(content=prompt)
            ]
            response = self.llm.invoke(messages)
            
            # Create a response in a format similar to the agent's
            yield {"messages": [response]}
            return
        except Exception as e:
            logger.error(f"Error in direct LLM invocation: {e}")
            yield {"error": str(e)}
    
    def _construct_legal_prompt(self, query: LegalQuery) -> str:
        """
        Construct a highly accurate legal analysis prompt for police and law enforcement use.
        
        Args:
            query: The LegalQuery object
            
        Returns:
            A formatted prompt string optimized for maximum accuracy
        """
        prompt = f"""
You are a Senior Legal Advisor and Expert in Indian Criminal Law with 25+ years of experience working with Police, CBI, ED, and other law enforcement agencies. You specialize in the NEW CRIMINAL LAWS that came into effect on July 1, 2024:

- BHARATIYA NYAYA SANHITA (BNS) 2023 - Replaced IPC
- BHARATIYA NAGARIK SURAKSHA SANHITA (BNSS) 2023 - Replaced CrPC  
- BHARATIYA SAKSHYA ADHINIYAM (BSA) 2023 - Replaced Evidence Act

CRITICAL INSTRUCTIONS FOR MAXIMUM ACCURACY:

1. **COMPREHENSIVE SECTION IDENTIFICATION** (Use ONLY BNS 2023 sections):
   
   **COMMON POLICE SECTIONS - BNS 2023:**
   - **BNS 103** (Murder) - formerly IPC 302
   - **BNS 109** (Culpable homicide not amounting to murder) - formerly IPC 304
   - **BNS 124** (Voluntarily causing hurt) - formerly IPC 323
   - **BNS 115** (Voluntarily causing grievous hurt) - formerly IPC 322
   - **BNS 64** (Rape) - formerly IPC 376
   - **BNS 303** (Theft) - formerly IPC 378
   - **BNS 304** (Theft in dwelling house) - formerly IPC 380
   - **BNS 305** (Theft by servant) - formerly IPC 381
   - **BNS 308** (Extortion) - formerly IPC 384
   - **BNS 309** (Robbery) - formerly IPC 392
   - **BNS 310** (Dacoity) - formerly IPC 395
   - **BNS 318** (Cheating) - formerly IPC 420
   - **BNS 319** (Cheating by personation) - formerly IPC 416
   - **BNS 329** (House-trespass) - formerly IPC 448
   - **BNS 331** (House-breaking) - formerly IPC 454
   - **BNS 335** (Mischief) - formerly IPC 425
   - **BNS 336** (Mischief causing damage) - formerly IPC 426
   - **BNS 351** (Criminal intimidation) - formerly IPC 506
   - **BNS 352** (Criminal intimidation by anonymous communication) - formerly IPC 507
   - **BNS 356** (Defamation) - formerly IPC 499
   - **BNS 132** (Assault) - formerly IPC 351
   - **BNS 196** (Promoting enmity between groups) - formerly IPC 153A
   - **BNS 197** (Imputations prejudicial to national integration) - formerly IPC 153B
   - **BNS 152** (Unlawful assembly) - formerly IPC 141
   - **BNS 189** (Public nuisance) - formerly IPC 268
   - **BNS 223** (Disobedience to order) - formerly IPC 188
   - **BNS 74** (Outraging modesty) - formerly IPC 354
   - **BNS 75** (Assault with intent to outrage modesty) - formerly IPC 354A
   - **BNS 77** (Voyeurism) - formerly IPC 354C
   - **BNS 78** (Stalking) - formerly IPC 354D

   **CYBER CRIME SECTIONS - BNS 2023:**
   - **BNS 318** (Cheating) - for online fraud
   - **BNS 319** (Cheating by personation) - for identity theft
   - **BNS 356** (Defamation) - for cyber defamation
   - **BNS 351** (Criminal intimidation) - for cyber threats
   - **BNS 75** (Assault with intent to outrage modesty) - for cyber harassment

   **ECONOMIC OFFENCES - BNS 2023:**
   - **BNS 318** (Cheating) - financial fraud
   - **BNS 308** (Extortion) - financial extortion
   - **BNS 303** (Theft) - financial theft
   - **BNS 336** (Mischief causing damage) - property damage

2. **POLICE INVESTIGATION PROCEDURES** (Use ONLY BNSS 2023 sections):
   
   **ESSENTIAL POLICE PROCEDURES - BNSS 2023:**
   - **BNSS 173** (FIR registration) - formerly CrPC 154
   - **BNSS 174** (Information to Magistrate) - formerly CrPC 155
   - **BNSS 176** (Investigation by police) - formerly CrPC 156
   - **BNSS 180** (Power to investigate) - formerly CrPC 157
   - **BNSS 35** (Arrest without warrant) - formerly CrPC 41
   - **BNSS 36** (Arrest with warrant) - formerly CrPC 42
   - **BNSS 37** (Arrest procedure) - formerly CrPC 46
   - **BNSS 187** (Search and seizure) - formerly CrPC 165
   - **BNSS 93** (Summons) - formerly CrPC 61
   - **BNSS 94** (Warrant) - formerly CrPC 70
   - **BNSS 479** (Bail provisions) - formerly CrPC 437
   - **BNSS 480** (Anticipatory bail) - formerly CrPC 438
   - **BNSS 183** (Recording statements) - formerly CrPC 161
   - **BNSS 184** (Examination of witnesses) - formerly CrPC 162
   - **BNSS 193** (Seizure of property) - formerly CrPC 102
   - **BNSS 194** (Search of place) - formerly CrPC 103

3. **EVIDENCE COLLECTION** (Use ONLY BSA 2023 sections):
   
   **EVIDENCE PROCEDURES - BSA 2023:**
   - **BSA 63** (Digital evidence) - NEW provision for electronic evidence
   - **BSA 64** (Electronic records) - NEW provision for digital records
   - **BSA 65** (Admissibility of electronic evidence) - NEW provision
   - **BSA 3** (Facts) - formerly Evidence Act 3
   - **BSA 45** (Expert opinion) - formerly Evidence Act 45
   - **BSA 46** (Facts bearing on expert opinion) - formerly Evidence Act 46
   - **BSA 59** (Dying declaration) - formerly Evidence Act 32
   - **BSA 22** (Admissions) - formerly Evidence Act 17
   - **BSA 24** (Confessions) - formerly Evidence Act 21
   - **BSA 25** (Confession to police) - formerly Evidence Act 25
   - **BSA 26** (Confession in custody) - formerly Evidence Act 26

4. **PUNISHMENT AND PENALTIES** (Exact BNS 2023 provisions):
   For each applicable section, provide:
   - Minimum punishment
   - Maximum punishment  
   - Fine amounts (if applicable)
   - Imprisonment type (simple/rigorous)
   - Cognizable/Non-cognizable status
   - Bailable/Non-bailable status
   - Compoundable/Non-compoundable status

5. **PRACTICAL POLICE GUIDANCE**:
   - Which sections to invoke for FIR
   - Investigation steps under BNSS
   - Evidence collection under BSA
   - Arrest procedures and powers
   - Search and seizure guidelines
   - Jurisdictional considerations
   - Time limits and statutory requirements

6. **MANDATORY FORMAT**:
   Use this exact format for each section:
   "**BNS Section XXX** (formerly IPC Section YYY) - [Description]"
   "**BNSS Section XXX** (formerly CrPC Section YYY) - [Description]"
   "**BSA Section XXX** (formerly Evidence Act Section YYY) - [Description]"

**CASE ANALYSIS:**
{query.query_text}

**PROVIDE COMPREHENSIVE ANALYSIS COVERING:**

**A. APPLICABLE CRIMINAL SECTIONS (BNS 2023)**
- List ALL applicable BNS sections with exact section numbers
- Explain why each section applies to the specific facts
- Provide punishment details for each section
- Mention cognizable/bailable status

**B. INVESTIGATION PROCEDURES (BNSS 2023)**
- FIR registration procedures
- Investigation powers and limitations
- Arrest procedures and conditions
- Search and seizure guidelines
- Evidence collection requirements

**C. EVIDENCE REQUIREMENTS (BSA 2023)**
- Types of evidence needed
- Admissibility requirements
- Digital evidence handling (if applicable)
- Witness examination procedures
- Documentary evidence requirements

**D. PRACTICAL POLICE ACTION PLAN**
- Immediate steps to be taken
- Investigation strategy
- Evidence preservation
- Legal compliance checklist
- Potential challenges and solutions

**E. JURISDICTIONAL AND PROCEDURAL CONSIDERATIONS**
- Territorial jurisdiction
- Court jurisdiction
- Time limitations
- Bail considerations
- Charge sheet requirements

ENSURE 100% ACCURACY - This analysis will be used by law enforcement officers for actual case handling.
"""
        return prompt


class BNSAdvisor:
    """
    Main class for the BNSAdvisor legal assistant application.
    Serves as the facade for all legal analysis capabilities.
    """
    
    def __init__(self):
        """Initialize the BNSAdvisor application."""
        logger.info("Initializing BNSAdvisor Legal Assistant")
        self.legal_agent = LegalAnalysisAgent()
        self.verifier = LegalVerifier()
        
        # Comprehensive BNS section mapping for maximum accuracy
        self.bns_section_mapping = {
            # Murder and Culpable Homicide
            "103": {"name": "Murder", "ipc_equivalent": "302", "punishment": "Death or life imprisonment", "cognizable": True, "bailable": False},
            "109": {"name": "Culpable homicide not amounting to murder", "ipc_equivalent": "304", "punishment": "Imprisonment up to 10 years", "cognizable": True, "bailable": False},
            
            # Hurt and Grievous Hurt
            "124": {"name": "Voluntarily causing hurt", "ipc_equivalent": "323", "punishment": "Imprisonment up to 1 year or fine up to Rs. 1000", "cognizable": True, "bailable": True},
            "115": {"name": "Voluntarily causing grievous hurt", "ipc_equivalent": "322", "punishment": "Imprisonment up to 7 years", "cognizable": True, "bailable": False},
            
            # Sexual Offences
            "64": {"name": "Rape", "ipc_equivalent": "376", "punishment": "Rigorous imprisonment not less than 10 years", "cognizable": True, "bailable": False},
            "74": {"name": "Assault or criminal force to woman with intent to outrage her modesty", "ipc_equivalent": "354", "punishment": "Imprisonment up to 2 years", "cognizable": True, "bailable": False},
            "75": {"name": "Sexual harassment", "ipc_equivalent": "354A", "punishment": "Imprisonment up to 3 years", "cognizable": True, "bailable": False},
            "77": {"name": "Voyeurism", "ipc_equivalent": "354C", "punishment": "Imprisonment up to 3 years", "cognizable": True, "bailable": False},
            "78": {"name": "Stalking", "ipc_equivalent": "354D", "punishment": "Imprisonment up to 3 years", "cognizable": True, "bailable": False},
            
            # Property Offences
            "303": {"name": "Theft", "ipc_equivalent": "378", "punishment": "Imprisonment up to 3 years", "cognizable": True, "bailable": True},
            "304": {"name": "Theft in dwelling house", "ipc_equivalent": "380", "punishment": "Imprisonment up to 7 years", "cognizable": True, "bailable": False},
            "305": {"name": "Theft by clerk or servant", "ipc_equivalent": "381", "punishment": "Imprisonment up to 7 years", "cognizable": True, "bailable": False},
            "308": {"name": "Extortion", "ipc_equivalent": "384", "punishment": "Imprisonment up to 3 years", "cognizable": True, "bailable": True},
            "309": {"name": "Robbery", "ipc_equivalent": "392", "punishment": "Imprisonment up to 10 years", "cognizable": True, "bailable": False},
            "310": {"name": "Dacoity", "ipc_equivalent": "395", "punishment": "Imprisonment for life or up to 10 years", "cognizable": True, "bailable": False},
            
            # Fraud and Cheating
            "318": {"name": "Cheating", "ipc_equivalent": "420", "punishment": "Imprisonment up to 7 years and fine", "cognizable": True, "bailable": False},
            "319": {"name": "Cheating by personation", "ipc_equivalent": "416", "punishment": "Imprisonment up to 5 years", "cognizable": True, "bailable": False},
            
            # Criminal Trespass and House-breaking
            "329": {"name": "Criminal trespass", "ipc_equivalent": "447", "punishment": "Imprisonment up to 3 months or fine up to Rs. 500", "cognizable": True, "bailable": True},
            "331": {"name": "House-breaking", "ipc_equivalent": "454", "punishment": "Imprisonment up to 2 years", "cognizable": True, "bailable": False},
            
            # Mischief
            "335": {"name": "Mischief", "ipc_equivalent": "425", "punishment": "Imprisonment up to 3 months or fine", "cognizable": True, "bailable": True},
            "336": {"name": "Mischief causing damage", "ipc_equivalent": "426", "punishment": "Imprisonment up to 1 year or fine", "cognizable": True, "bailable": True},
            
            # Criminal Intimidation and Defamation
            "351": {"name": "Criminal intimidation", "ipc_equivalent": "506", "punishment": "Imprisonment up to 2 years", "cognizable": True, "bailable": True},
            "352": {"name": "Criminal intimidation by anonymous communication", "ipc_equivalent": "507", "punishment": "Imprisonment up to 2 years", "cognizable": True, "bailable": True},
            "356": {"name": "Defamation", "ipc_equivalent": "499", "punishment": "Simple imprisonment up to 2 years or fine", "cognizable": False, "bailable": True},
            
            # Assault and Force
            "132": {"name": "Assault or criminal force", "ipc_equivalent": "351", "punishment": "Imprisonment up to 3 months or fine up to Rs. 500", "cognizable": True, "bailable": True},
            
            # Public Order
            "152": {"name": "Unlawful assembly", "ipc_equivalent": "141", "punishment": "Imprisonment up to 6 months or fine", "cognizable": True, "bailable": True},
            "189": {"name": "Public nuisance", "ipc_equivalent": "268", "punishment": "Fine up to Rs. 200", "cognizable": False, "bailable": True},
            "196": {"name": "Promoting enmity between different groups", "ipc_equivalent": "153A", "punishment": "Imprisonment up to 3 years", "cognizable": True, "bailable": False},
            "197": {"name": "Imputations, assertions prejudicial to national integration", "ipc_equivalent": "153B", "punishment": "Imprisonment up to 3 years", "cognizable": True, "bailable": False},
            "223": {"name": "Disobedience to order duly promulgated by public servant", "ipc_equivalent": "188", "punishment": "Simple imprisonment up to 1 month or fine up to Rs. 200", "cognizable": True, "bailable": True},
        }
        
        # BNSS section mapping for procedures
        self.bnss_section_mapping = {
            "173": {"name": "Information in cognizable cases", "crpc_equivalent": "154", "description": "FIR registration"},
            "174": {"name": "Information to Magistrate", "crpc_equivalent": "155", "description": "Informing magistrate of cognizable case"},
            "176": {"name": "Investigation by police", "crpc_equivalent": "156", "description": "Police investigation powers"},
            "180": {"name": "Power of police officer to investigate cognizable case", "crpc_equivalent": "157", "description": "Investigation authority"},
            "35": {"name": "Arrest without warrant", "crpc_equivalent": "41", "description": "Arrest powers without warrant"},
            "36": {"name": "Arrest with warrant", "crpc_equivalent": "42", "description": "Arrest with warrant"},
            "37": {"name": "How to arrest", "crpc_equivalent": "46", "description": "Arrest procedures"},
            "187": {"name": "Search and seizure", "crpc_equivalent": "165", "description": "Search and seizure powers"},
            "479": {"name": "Bail", "crpc_equivalent": "437", "description": "Bail provisions"},
            "480": {"name": "Anticipatory bail", "crpc_equivalent": "438", "description": "Anticipatory bail"},
        }
        
        # BSA section mapping for evidence
        self.bsa_section_mapping = {
            "63": {"name": "Admissibility of electronic records", "evidence_act_equivalent": "65A", "description": "Electronic evidence admissibility"},
            "64": {"name": "Special provisions as to evidence relating to electronic record", "evidence_act_equivalent": "65B", "description": "Electronic records evidence"},
            "65": {"name": "Presumption as to electronic records", "evidence_act_equivalent": "88A", "description": "Electronic records presumption"},
            "3": {"name": "Facts", "evidence_act_equivalent": "3", "description": "Definition of facts"},
            "45": {"name": "Opinions of experts", "evidence_act_equivalent": "45", "description": "Expert testimony"},
            "46": {"name": "Facts bearing upon opinions of experts", "evidence_act_equivalent": "46", "description": "Expert opinion facts"},
            "59": {"name": "Statements made by persons who cannot be called as witnesses", "evidence_act_equivalent": "32", "description": "Dying declaration"},
            "22": {"name": "Admission defined", "evidence_act_equivalent": "17", "description": "Admissions"},
            "24": {"name": "Confession defined", "evidence_act_equivalent": "21", "description": "Confessions"},
            "25": {"name": "Confession to police", "evidence_act_equivalent": "25", "description": "Police confession"},
            "26": {"name": "Confession by accused while in custody of police", "evidence_act_equivalent": "26", "description": "Custody confession"},
        }
        
    def get_section_details(self, code: str, section: str) -> Dict[str, Any]:
        """
        Get detailed information about a specific legal section.
        
        Args:
            code: Legal code (BNS, BNSS, BSA)
            section: Section number
        
        Returns:
            Dictionary with section details
        """
        # Forward mapping for new codes to include legacy equivalents
        if code == "BNS" and section in self.bns_section_mapping:
            details = self.bns_section_mapping[section].copy()
            return {
                "new_code": "BNS",
                "new_section": section,
                "legacy_code": "IPC",
                "legacy_section": details.get("ipc_equivalent"),
                **details
            }
        # Reverse mapping from legacy IPC to new BNS
        if code == "IPC":
            for new_sec, val in self.bns_section_mapping.items():
                if val.get("ipc_equivalent") == section:
                    details = val.copy()
                    return {
                        "new_code": "BNS",
                        "new_section": new_sec,
                        "legacy_code": "IPC",
                        "legacy_section": section,
                        **details
                    }
        # Forward mapping for BNSS new code with CrPC legacy
        if code == "BNSS" and section in self.bnss_section_mapping:
            details = self.bnss_section_mapping[section].copy()
            return {
                "new_code": "BNSS",
                "new_section": section,
                "legacy_code": "CrPC",
                "legacy_section": details.get("crpc_equivalent"),
                **details
            }
        # Reverse mapping from legacy CrPC to new BNSS
        if code == "CrPC":
            for new_sec, val in self.bnss_section_mapping.items():
                if val.get("crpc_equivalent") == section:
                    details = val.copy()
                    return {
                        "new_code": "BNSS",
                        "new_section": new_sec,
                        "legacy_code": "CrPC",
                        "legacy_section": section,
                        **details
                    }
        # Forward mapping for BSA new code with Evidence Act legacy
        if code == "BSA" and section in self.bsa_section_mapping:
            details = self.bsa_section_mapping[section].copy()
            return {
                "new_code": "BSA",
                "new_section": section,
                "legacy_code": "Evidence Act",
                "legacy_section": details.get("evidence_act_equivalent"),
                **details
            }
        # Reverse mapping from legacy Evidence Act to new BSA
        if code == "Evidence Act":
            for new_sec, val in self.bsa_section_mapping.items():
                if val.get("evidence_act_equivalent") == section:
                    details = val.copy()
                    return {
                        "new_code": "BSA",
                        "new_section": new_sec,
                        "legacy_code": "Evidence Act",
                        "legacy_section": section,
                        **details
                    }
        # Fallback for unknown sections
        return {"name": "Unknown section", "description": "Section details not available"}
    
    def find_supporting_evidence(self, reference):
        """
        Find supporting evidence for a legal reference with enhanced accuracy for law enforcement.
        
        Args:
            reference: Legal reference to find evidence for
            
        Returns:
            List of supporting evidence
        """
        if not self.search_tool:
            logger.warning("Search tool unavailable for finding supporting evidence")
            return []
        
        try:
            # Construct enhanced search query for law enforcement
            code = reference["code"]
            section = reference["section"]
            
            # Get section details for better search
            section_details = self.get_section_details(code, section)
            section_name = section_details.get("name", "")
            
            # Create comprehensive search query
            if code in ["BNS", "BNSS", "BSA"]:
                query = f"{code} Section {section} {section_name} Indian criminal law police procedure"
                
                # Add specific terms for better results
                if code == "BNS":
                    query += " Bharatiya Nyaya Sanhita 2023 criminal offense"
                elif code == "BNSS":
                    query += " Bharatiya Nagarik Suraksha Sanhita 2023 police procedure"
                elif code == "BSA":
                    query += " Bharatiya Sakshya Adhiniyam 2023 evidence law"
            else:
                query = f"{code} Section {section} Indian legal code reference"
            
            # Perform search
            search_results = self.search_tool.run(query)
            
            # Parse results
            evidence = []
            if isinstance(search_results, str):
                # Extract URLs from text results
                urls = re.findall(r'https?://[^\s]+', search_results)
                titles = re.findall(r'(?:^|\n)([^.\n]+)(?=\.)', search_results)
                
                for i, url in enumerate(urls[:5]):  # Limit to top 5 for better quality
                    title = titles[i] if i < len(titles) else f"{code} Section {section} - {section_name}"
                    if title and url and "error" not in title.lower():
                        evidence.append({
                            "title": title,
                            "link": url,
                            "section_details": section_details
                        })
            else:
                # Handle structured results
                for item in search_results[:5]:  # Limit to top 5
                    title = item.get("title", "")
                    link = item.get("link", "")
                    if title and link and "error" not in title.lower():
                        evidence.append({
                            "title": title,
                            "link": link,
                            "section_details": section_details
                        })
            
            return evidence
        except Exception as e:
            logger.error(f"Error finding supporting evidence for {reference.get('code', 'unknown')} Section {reference.get('section', 'unknown')}: {e}")
            return []
    
    def analyze_case(self, case_description: str, verify: bool = True) -> Dict[str, Any]:
        """
        Analyze a legal case and provide comprehensive advice.
        
        Args:
            case_description: A string description of the legal case
            verify: Whether to verify legal references with supporting evidence
            
        Returns:
            A dictionary with the analysis results
        """
        logger.info(f"Analyzing case: {case_description[:50]}...")
        result = self.legal_agent.analyze_case(case_description)
        
        # Add verification if requested
        if verify and "agent" in result and "messages" in result["agent"] and result["agent"]["messages"]:
            message = result["agent"]["messages"][-1]
            if hasattr(message, 'content') and message.content:
                verified_content = self.verifier.verify_analysis(message.content)
                # Create a new message with the verified content
                new_message = AIMessage(content=verified_content)
                result["agent"]["messages"][-1] = new_message
        
        return result
    
    def stream_analysis(self, case_description: str, verify: bool = True):
        """
        Stream the analysis of a legal case as it is generated.
        
        Args:
            case_description: A string description of the legal case
            verify: Whether to verify legal references with supporting evidence
            
        Returns:
            A generator yielding chunks of the analysis
        """
        logger.info(f"Streaming analysis for case: {case_description[:50]}...")
        
        # Get all chunks from the legal agent without modifying them
        legal_chunks = list(self.legal_agent.stream_analysis(case_description))
        
        # If verification is requested and we have at least one chunk
        if verify and legal_chunks:
            last_chunk = legal_chunks[-1]
            
            # Extract the content from the last chunk
            content = None
            if "agent" in last_chunk and "messages" in last_chunk["agent"] and last_chunk["agent"]["messages"]:
                message = last_chunk["agent"]["messages"][-1]
                if hasattr(message, 'content'):
                    content = message.content
            elif "messages" in last_chunk and last_chunk["messages"]:
                message = last_chunk["messages"][-1]
                if hasattr(message, 'content'):
                    content = message.content
            
            # Verify the content if available
            if content:
                logger.info("Verifying legal references with supporting evidence")
                verified_content = self.verifier.verify_analysis(content)
                
                # Update the last chunk with verified content
                if "agent" in last_chunk and "messages" in last_chunk["agent"] and last_chunk["agent"]["messages"]:
                    last_chunk["agent"]["messages"][-1] = AIMessage(content=verified_content)
                elif "messages" in last_chunk and last_chunk["messages"]:
                    last_chunk["messages"][-1] = AIMessage(content=verified_content)
            
        # Yield all chunks except the last one
        for chunk in legal_chunks[:-1]:
            yield chunk
        
        # Yield the possibly modified last chunk
        if legal_chunks:
            yield legal_chunks[-1]
    
    async def analyze_case_async(self, case_description: str, verify: bool = True) -> Dict[str, Any]:
        """
        Asynchronously analyze a legal case.
        
        Args:
            case_description: A string description of the legal case
            verify: Whether to verify legal references with supporting evidence
            
        Returns:
            A dictionary with the analysis results
        """
        logger.info(f"Asynchronously analyzing case: {case_description[:50]}...")
        result = await self.legal_agent.analyze_case_async(case_description)
        
        # Add verification if requested
        if verify and "agent" in result and "messages" in result["agent"] and result["agent"]["messages"]:
            message = result["agent"]["messages"][-1]
            if hasattr(message, 'content') and message.content:
                verified_content = self.verifier.verify_analysis(message.content)
                # Create a new message with the verified content
                new_message = AIMessage(content=verified_content)
                result["agent"]["messages"][-1] = new_message
        
        return result


# Example usage
if __name__ == "__main__":
    # Complex legal case involving multiple offenses, digital evidence, and procedural aspects
    case_description = """
    A sophisticated cybercrime and fraud case where:
    
    1. Primary Offense:
    - A group of 4 individuals created a fake cryptocurrency investment platform
    - Used AI-generated deep fake videos of prominent business leaders endorsing their platform
    - Collected approximately ₹50 crore from 2000+ investors over 8 months
    - Operated through a network of shell companies registered in multiple states
    
    2. Methods Used:
    - Sophisticated phishing attacks targeting HNI investors
    - Created fake trading volumes using automated bots
    - Used encrypted communication channels for internal coordination
    - Laundered money through multiple crypto exchanges and foreign bank accounts
    
    3. Additional Criminal Acts:
    - Threatened whistleblowers who tried to expose the scheme
    - Bribed some local officials to avoid regulatory scrutiny
    - Used stolen KYC documents to create fake director profiles
    - Intimidated investors who demanded refunds
    
    4. Evidence Available:
    - Digital footprints on blockchain transactions
    - Server logs from the trading platform
    - WhatsApp and Telegram chat transcripts
    - Bank transaction records
    - Deep fake video creation tools found on seized laptops
    - Witness statements from 15 victims
    - Documentary evidence of shell companies
    - Call recordings of threats made to investors
    
    5. Current Status:
    - Main accused arrested while attempting to flee the country
    - ₹15 crore in crypto assets frozen across exchanges
    - Two accused still absconding, possibly in a foreign jurisdiction
    - Multiple victims have filed complaints in different states
    
    Need comprehensive legal analysis covering all applicable sections under BNS, BNSS, and BSA, including:
    - All applicable criminal charges
    - Procedural aspects for multi-state investigation
    - Digital evidence handling requirements
    - Jurisdictional considerations
    - Provisions for asset recovery
    - International cooperation mechanisms
    """
    
    # Initialize BNSAdvisor and get response
    bnsAdvisor = BNSAdvisor()
    result = bnsAdvisor.analyze_case(case_description, verify=True)
    
    # Display only the final response content
    if "agent" in result and "messages" in result["agent"] and result["agent"]["messages"]:
        print(result["agent"]["messages"][-1].content)
    elif "messages" in result:
        for msg in result["messages"]:
            if hasattr(msg, 'content') and msg.content:
                print(msg.content)
    else:
        print(result)